using Microsoft.AspNetCore.Mvc;
using CIAPI.Shared.DTOs.Common;
using CIAPI.Modules.ProductManagement.Application.DTOs;
using CIAPI.Modules.ProductManagement.Application.Services;

namespace CIAPI.Web.Controllers;

/// <summary>
/// Investigators management controller
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class InvestigatorsController : ControllerBase
{
    private readonly IInvestigatorService _investigatorService;
    private readonly ILogger<InvestigatorsController> _logger;

    public InvestigatorsController(IInvestigatorService investigatorService, ILogger<InvestigatorsController> logger)
    {
        _investigatorService = investigatorService ?? throw new ArgumentNullException(nameof(investigatorService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Get all investigators using stored procedure with pagination
    /// </summary>
    /// <param name="pageNumber">Page number (default: 1)</param>
    /// <param name="pageSize">Page size (default: 10, max: 100)</param>
    /// <param name="searchTerm">Search term for filtering investigators</param>
    /// <param name="regionFilter">Region filter</param>
    /// <param name="countryFilter">Country filter</param>
    /// <returns>Paginated list of investigators</returns>
    [HttpGet]
    [ProducesResponseType(typeof(ApiResponse<PagedResult<InvestigatorDto>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<PagedResult<InvestigatorDto>>>> GetInvestigators(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? searchTerm = null,
        [FromQuery] string? regionFilter = null,
        [FromQuery] string? countryFilter = null)
    {
        try
        {
            _logger.LogInformation("Getting investigators with pagination. Page: {PageNumber}, Size: {PageSize}, Search: {SearchTerm}", 
                pageNumber, pageSize, searchTerm);

            // Validate pagination parameters
            if (pageNumber < 1)
                return BadRequest(ApiResponse<PagedResult<InvestigatorDto>>.ErrorResult("Page number must be greater than 0"));

            if (pageSize < 1 || pageSize > 100)
                return BadRequest(ApiResponse<PagedResult<InvestigatorDto>>.ErrorResult("Page size must be between 1 and 100"));

            var request = new InvestigatorSearchRequest
            {
                PageNumber = pageNumber,
                PageSize = pageSize,
                SearchTerm = searchTerm,
                RegionFilter = regionFilter,
                CountryFilter = countryFilter
            };

            var response = await _investigatorService.GetInvestigatorsSamplePagedAsync(request);

            if (response.Success)
                return Ok(response);

            return StatusCode(500, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting investigators");
            return StatusCode(500, ApiResponse<PagedResult<InvestigatorDto>>.ErrorResult("An error occurred while retrieving investigators"));
        }
    }

    /// <summary>
    /// Get all investigators using stored procedure (non-paginated)
    /// </summary>
    /// <returns>List of all investigators</returns>
    [HttpGet("sample")]
    [ProducesResponseType(typeof(ApiResponse<IEnumerable<InvestigatorDto>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<IEnumerable<InvestigatorDto>>>> GetInvestigatorsSample()
    {
        try
        {
            _logger.LogInformation("Getting all investigators using GetInvestigators_Sample stored procedure");

            var response = await _investigatorService.GetInvestigatorsSampleAsync();

            if (response.Success)
                return Ok(response);

            return StatusCode(500, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting investigators from stored procedure");
            return StatusCode(500, ApiResponse<IEnumerable<InvestigatorDto>>.ErrorResult("An error occurred while retrieving investigators"));
        }
    }

    /// <summary>
    /// Get investigator by ID
    /// </summary>
    /// <param name="id">Investigator ID</param>
    /// <returns>Investigator details</returns>
    [HttpGet("{id:int}")]
    [ProducesResponseType(typeof(ApiResponse<InvestigatorDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<InvestigatorDto>>> GetInvestigator(int id)
    {
        try
        {
            _logger.LogInformation("Getting investigator with ID: {InvestigatorId}", id);

            if (id <= 0)
                return BadRequest(ApiResponse<InvestigatorDto>.ErrorResult("Invalid investigator ID"));

            var response = await _investigatorService.GetInvestigatorByIdAsync(id);

            if (response.Success)
                return Ok(response);

            if (response.Message.Contains("not found"))
                return NotFound(response);

            return StatusCode(500, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting investigator with ID: {InvestigatorId}", id);
            return StatusCode(500, ApiResponse<InvestigatorDto>.ErrorResult("An error occurred while retrieving the investigator"));
        }
    }

    /// <summary>
    /// Create a new investigator
    /// </summary>
    /// <param name="request">Investigator creation request</param>
    /// <returns>Created investigator details</returns>
    [HttpPost]
    [ProducesResponseType(typeof(ApiResponse<InvestigatorDto>), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<InvestigatorDto>>> CreateInvestigator([FromBody] CreateInvestigatorRequest request)
    {
        try
        {
            _logger.LogInformation("Creating new investigator: {InvestigatorName}", request.InvestigatorName);

            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values
                    .SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage);
                return BadRequest(ApiResponse<InvestigatorDto>.ErrorResult(errors));
            }

            var response = await _investigatorService.CreateInvestigatorAsync(request);

            if (response.Success && response.Data != null)
            {
                return CreatedAtAction(
                    nameof(GetInvestigator),
                    new { id = response.Data.Id },
                    response);
            }

            return StatusCode(500, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while creating investigator");
            return StatusCode(500, ApiResponse<InvestigatorDto>.ErrorResult("An error occurred while creating the investigator"));
        }
    }

    /// <summary>
    /// Search investigators by name
    /// </summary>
    /// <param name="searchTerm">Search term</param>
    /// <returns>List of matching investigators</returns>
    [HttpGet("search")]
    [ProducesResponseType(typeof(ApiResponse<IEnumerable<InvestigatorDto>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<IEnumerable<InvestigatorDto>>>> SearchInvestigators([FromQuery] string searchTerm)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return BadRequest(ApiResponse<IEnumerable<InvestigatorDto>>.ErrorResult("Search term is required"));

            _logger.LogInformation("Searching investigators by name: {SearchTerm}", searchTerm);

            var response = await _investigatorService.SearchInvestigatorsByNameAsync(searchTerm);

            if (response.Success)
                return Ok(response);

            return StatusCode(500, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while searching investigators");
            return StatusCode(500, ApiResponse<IEnumerable<InvestigatorDto>>.ErrorResult("An error occurred while searching investigators"));
        }
    }

    /// <summary>
    /// Get investigators by region
    /// </summary>
    /// <param name="regionName">Region name</param>
    /// <returns>List of investigators in the region</returns>
    [HttpGet("region/{regionName}")]
    [ProducesResponseType(typeof(ApiResponse<IEnumerable<InvestigatorDto>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<IEnumerable<InvestigatorDto>>>> GetInvestigatorsByRegion(string regionName)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(regionName))
                return BadRequest(ApiResponse<IEnumerable<InvestigatorDto>>.ErrorResult("Region name is required"));

            _logger.LogInformation("Getting investigators by region: {RegionName}", regionName);

            var response = await _investigatorService.GetInvestigatorsByRegionAsync(regionName);

            if (response.Success)
                return Ok(response);

            return StatusCode(500, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting investigators by region");
            return StatusCode(500, ApiResponse<IEnumerable<InvestigatorDto>>.ErrorResult("An error occurred while retrieving investigators"));
        }
    }

    /// <summary>
    /// Get distinct regions
    /// </summary>
    /// <returns>List of distinct regions</returns>
    [HttpGet("filters/regions")]
    [ProducesResponseType(typeof(ApiResponse<IEnumerable<string>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<IEnumerable<string>>>> GetDistinctRegions()
    {
        try
        {
            _logger.LogInformation("Getting distinct regions");

            var response = await _investigatorService.GetDistinctRegionsAsync();

            if (response.Success)
                return Ok(response);

            return StatusCode(500, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting distinct regions");
            return StatusCode(500, ApiResponse<IEnumerable<string>>.ErrorResult("An error occurred while retrieving regions"));
        }
    }

    /// <summary>
    /// Get distinct countries
    /// </summary>
    /// <returns>List of distinct countries</returns>
    [HttpGet("filters/countries")]
    [ProducesResponseType(typeof(ApiResponse<IEnumerable<string>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<IEnumerable<string>>>> GetDistinctCountries()
    {
        try
        {
            _logger.LogInformation("Getting distinct countries");

            var response = await _investigatorService.GetDistinctCountriesAsync();

            if (response.Success)
                return Ok(response);

            return StatusCode(500, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting distinct countries");
            return StatusCode(500, ApiResponse<IEnumerable<string>>.ErrorResult("An error occurred while retrieving countries"));
        }
    }

    /// <summary>
    /// Get all investigator filters
    /// </summary>
    /// <returns>Investigator filters including regions, countries, and specializations</returns>
    [HttpGet("filters")]
    [ProducesResponseType(typeof(ApiResponse<InvestigatorFiltersDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<InvestigatorFiltersDto>>> GetInvestigatorFilters()
    {
        try
        {
            _logger.LogInformation("Getting investigator filters");

            var response = await _investigatorService.GetInvestigatorFiltersAsync();

            if (response.Success)
                return Ok(response);

            return StatusCode(500, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting investigator filters");
            return StatusCode(500, ApiResponse<InvestigatorFiltersDto>.ErrorResult("An error occurred while retrieving filters"));
        }
    }

    /// <summary>
    /// Health check for Investigators module
    /// </summary>
    /// <returns>Health status</returns>
    [HttpGet("health")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    public ActionResult GetHealth()
    {
        return Ok(new
        {
            Module = "ProductManagement.Investigators",
            Status = "Healthy",
            Timestamp = DateTime.UtcNow,
            Version = "1.0.0",
            StoredProcedure = "GetInvestigators_Sample"
        });
    }
}
