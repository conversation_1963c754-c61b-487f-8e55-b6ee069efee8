using CIAPI.Modules.ProductManagement.Domain.Entities;
using CIAPI.Modules.ProductManagement.Domain.Interfaces;
using CIAPI.Modules.ProductManagement.Infrastructure.Data;
using Dapper;
using System.Linq.Expressions;

namespace CIAPI.Modules.ProductManagement.Infrastructure.Repositories;

/// <summary>
/// Base repository implementation for ProductManagement module using ADO.NET with Dapper
/// </summary>
/// <typeparam name="T">Entity type that inherits from BaseEntity</typeparam>
public abstract class BaseProductRepository<T> : IRepository<T> where T : BaseEntity
{
    protected readonly IProductManagementDbConnectionFactory _connectionFactory;
    protected readonly string _tableName;

    protected BaseProductRepository(IProductManagementDbConnectionFactory connectionFactory, string tableName)
    {
        _connectionFactory = connectionFactory ?? throw new ArgumentNullException(nameof(connectionFactory));
        _tableName = tableName ?? throw new ArgumentNullException(nameof(tableName));
    }

    public virtual async Task<T?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = $"SELECT * FROM {_tableName} WHERE Id = @Id AND IsDeleted = 0";
        return await connection.QueryFirstOrDefaultAsync<T>(sql, new { Id = id });
    }

    public virtual async Task<IEnumerable<T>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = $"SELECT * FROM {_tableName} WHERE IsDeleted = 0 ORDER BY CreatedAt DESC";
        return await connection.QueryAsync<T>(sql);
    }

    public virtual async Task<T> AddAsync(T entity, CancellationToken cancellationToken = default)
    {
        entity.CreatedAt = DateTime.UtcNow;
        entity.IsActive = true;
        entity.IsDeleted = false;

        using var connection = _connectionFactory.CreateConnection();
        var insertSql = GenerateInsertSql();
        var id = await connection.QuerySingleAsync<int>(insertSql, entity);
        entity.Id = id;
        return entity;
    }

    public virtual async Task<T> UpdateAsync(T entity, CancellationToken cancellationToken = default)
    {
        entity.UpdatedAt = DateTime.UtcNow;

        using var connection = _connectionFactory.CreateConnection();
        var updateSql = GenerateUpdateSql();
        await connection.ExecuteAsync(updateSql, entity);
        return entity;
    }

    public virtual async Task DeleteAsync(int id, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = $"DELETE FROM {_tableName} WHERE Id = @Id";
        await connection.ExecuteAsync(sql, new { Id = id });
    }

    public virtual async Task DeleteAsync(T entity, CancellationToken cancellationToken = default)
    {
        await DeleteAsync(entity.Id, cancellationToken);
    }

    public virtual async Task SoftDeleteAsync(int id, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = $"UPDATE {_tableName} SET IsDeleted = 1, UpdatedAt = @UpdatedAt WHERE Id = @Id";
        await connection.ExecuteAsync(sql, new { Id = id, UpdatedAt = DateTime.UtcNow });
    }

    public virtual async Task SoftDeleteAsync(T entity, CancellationToken cancellationToken = default)
    {
        await SoftDeleteAsync(entity.Id, cancellationToken);
    }

    public virtual async Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        var count = await CountAsync(predicate, cancellationToken);
        return count > 0;
    }

    public virtual async Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = $"SELECT COUNT(*) FROM {_tableName} WHERE IsDeleted = 0";
        return await connection.QuerySingleAsync<int>(sql);
    }

    // Abstract methods to be implemented by derived classes
    protected abstract string GenerateInsertSql();
    protected abstract string GenerateUpdateSql();

    // Not implemented in base class - would need expression tree parsing
    public virtual Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("FindAsync with expression requires custom implementation in derived class");
    }

    public virtual Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("FirstOrDefaultAsync with expression requires custom implementation in derived class");
    }

    public virtual Task<(IEnumerable<T> Items, int TotalCount)> GetPagedAsync(int pageNumber, int pageSize, Expression<Func<T, bool>>? predicate = null, Expression<Func<T, object>>? orderBy = null, bool ascending = true, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("GetPagedAsync with expressions requires custom implementation in derived class");
    }

    public virtual Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("AddRangeAsync requires custom implementation in derived class");
    }

    public virtual Task<IEnumerable<T>> UpdateRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("UpdateRangeAsync requires custom implementation in derived class");
    }

    public virtual Task DeleteRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("DeleteRangeAsync requires custom implementation in derived class");
    }

    public virtual Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // ADO.NET doesn't have a change tracker, so this is not applicable
        return Task.FromResult(0);
    }
}

/// <summary>
/// Investigator repository implementation using ADO.NET with Dapper for ProductManagement module
/// </summary>
public class InvestigatorRepository : BaseProductRepository<Investigator>, IInvestigatorRepository
{
    public InvestigatorRepository(IProductManagementDbConnectionFactory connectionFactory) 
        : base(connectionFactory, "Investigators")
    {
    }

    public async Task<IEnumerable<Investigator>> GetInvestigatorsSampleAsync(CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        
        var sql = "EXEC GetInvestigators_Sample";
        
        return await connection.QueryAsync<Investigator>(sql);
    }

    public async Task<(IEnumerable<Investigator> Items, int TotalCount)> GetInvestigatorsSamplePagedAsync(
        int pageNumber = 1, 
        int pageSize = 10, 
        string? searchTerm = null,
        string? regionFilter = null,
        string? countryFilter = null,
        CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        
        // First get all data from stored procedure
        var allData = await connection.QueryAsync<Investigator>("EXEC GetInvestigators_Sample");
        
        // Apply filters in memory (for simplicity)
        var filteredData = allData.AsEnumerable();
        
        if (!string.IsNullOrEmpty(searchTerm))
        {
            filteredData = filteredData.Where(x => x.InvestigatorName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));
        }
        
        if (!string.IsNullOrEmpty(regionFilter))
        {
            filteredData = filteredData.Where(x => x.RegionName == regionFilter);
        }
        
        if (!string.IsNullOrEmpty(countryFilter))
        {
            filteredData = filteredData.Where(x => x.CountryName == countryFilter);
        }
        
        var totalCount = filteredData.Count();
        
        // Apply pagination
        var offset = (pageNumber - 1) * pageSize;
        var pagedData = filteredData
            .OrderBy(x => x.InvestigatorName)
            .Skip(offset)
            .Take(pageSize);
        
        return (pagedData, totalCount);
    }

    public async Task<IEnumerable<Investigator>> GetInvestigatorsByRegionAsync(string regionName, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        
        var allData = await connection.QueryAsync<Investigator>("EXEC GetInvestigators_Sample");
        
        return allData.Where(x => x.RegionName == regionName).OrderBy(x => x.InvestigatorName);
    }

    public async Task<IEnumerable<Investigator>> GetInvestigatorsByCountryAsync(string countryName, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        
        var allData = await connection.QueryAsync<Investigator>("EXEC GetInvestigators_Sample");
        
        return allData.Where(x => x.CountryName == countryName).OrderBy(x => x.InvestigatorName);
    }

    public async Task<IEnumerable<Investigator>> GetInvestigatorsBySpecializationAsync(string specialization, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        
        var allData = await connection.QueryAsync<Investigator>("EXEC GetInvestigators_Sample");
        
        return allData.Where(x => x.SpecializationNames?.Contains(specialization, StringComparison.OrdinalIgnoreCase) == true)
                     .OrderBy(x => x.InvestigatorName);
    }

    public async Task<IEnumerable<Investigator>> GetInvestigatorsByOrganisationAsync(string organisation, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        
        var allData = await connection.QueryAsync<Investigator>("EXEC GetInvestigators_Sample");
        
        return allData.Where(x => x.Organisation == organisation).OrderBy(x => x.InvestigatorName);
    }

    public async Task<IEnumerable<Investigator>> SearchInvestigatorsByNameAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        
        var allData = await connection.QueryAsync<Investigator>("EXEC GetInvestigators_Sample");
        
        return allData.Where(x => x.InvestigatorName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                     .OrderBy(x => x.InvestigatorName);
    }

    public async Task<IEnumerable<string>> GetDistinctRegionsAsync(CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        
        var allData = await connection.QueryAsync<Investigator>("EXEC GetInvestigators_Sample");
        
        return allData
            .Where(x => !string.IsNullOrEmpty(x.RegionName))
            .Select(x => x.RegionName!)
            .Distinct()
            .OrderBy(x => x);
    }

    public async Task<IEnumerable<string>> GetDistinctCountriesAsync(CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        
        var allData = await connection.QueryAsync<Investigator>("EXEC GetInvestigators_Sample");
        
        return allData
            .Where(x => !string.IsNullOrEmpty(x.CountryName))
            .Select(x => x.CountryName!)
            .Distinct()
            .OrderBy(x => x);
    }

    public async Task<IEnumerable<string>> GetDistinctSpecializationsAsync(CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        
        var allData = await connection.QueryAsync<Investigator>("EXEC GetInvestigators_Sample");
        
        return allData
            .Where(x => !string.IsNullOrEmpty(x.SpecializationNames))
            .Select(x => x.SpecializationNames!)
            .Distinct()
            .OrderBy(x => x);
    }

    protected override string GenerateInsertSql()
    {
        return @"
            INSERT INTO Investigators (InvestigatorName, SpecializationNames, Designation, Organisation, 
                                     ContactNumber, EmailID, Fax, RegionName, CountryName, StateName, CityName,
                                     CreatedAt, CreatedBy, IsActive, IsDeleted)
            VALUES (@InvestigatorName, @SpecializationNames, @Designation, @Organisation,
                   @ContactNumber, @EmailID, @Fax, @RegionName, @CountryName, @StateName, @CityName,
                   @CreatedAt, @CreatedBy, @IsActive, @IsDeleted);
            SELECT CAST(SCOPE_IDENTITY() as int);";
    }

    protected override string GenerateUpdateSql()
    {
        return @"
            UPDATE Investigators SET 
                InvestigatorName = @InvestigatorName, SpecializationNames = @SpecializationNames,
                Designation = @Designation, Organisation = @Organisation, ContactNumber = @ContactNumber,
                EmailID = @EmailID, Fax = @Fax, RegionName = @RegionName, CountryName = @CountryName,
                StateName = @StateName, CityName = @CityName, UpdatedAt = @UpdatedAt, UpdatedBy = @UpdatedBy,
                IsActive = @IsActive
            WHERE Id = @Id";
    }
}
