[{"ContainingType": "CIAPI.Web.Controllers.InvestigatorsController", "Method": "GetInvestigators", "RelativePath": "api/Investigators", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "regionFilter", "Type": "System.String", "IsRequired": false}, {"Name": "countryF<PERSON>er", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "CIAPI.Shared.DTOs.Common.ApiResponse`1[[CIAPI.Shared.DTOs.Common.PagedResult`1[[CIAPI.Modules.ProductManagement.Application.DTOs.InvestigatorDto, CIAPI.Modules.ProductManagement, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], CIAPI.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "CIAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "CIAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "CIAPI.Web.Controllers.InvestigatorsController", "Method": "CreateInvestigator", "RelativePath": "api/Investigators", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "CIAPI.Modules.ProductManagement.Application.DTOs.CreateInvestigatorRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "CIAPI.Shared.DTOs.Common.ApiResponse`1[[CIAPI.Modules.ProductManagement.Application.DTOs.InvestigatorDto, CIAPI.Modules.ProductManagement, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "CIAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "CIAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "CIAPI.Web.Controllers.InvestigatorsController", "Method": "GetInvestigator", "RelativePath": "api/Investigators/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "CIAPI.Shared.DTOs.Common.ApiResponse`1[[CIAPI.Modules.ProductManagement.Application.DTOs.InvestigatorDto, CIAPI.Modules.ProductManagement, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "CIAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "CIAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "CIAPI.Web.Controllers.InvestigatorsController", "Method": "GetInvestigatorFilters", "RelativePath": "api/Investigators/filters", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "CIAPI.Shared.DTOs.Common.ApiResponse`1[[CIAPI.Modules.ProductManagement.Application.DTOs.InvestigatorFiltersDto, CIAPI.Modules.ProductManagement, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "CIAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "CIAPI.Web.Controllers.InvestigatorsController", "Method": "GetDistinctCountries", "RelativePath": "api/Investigators/filters/countries", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "CIAPI.Shared.DTOs.Common.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "CIAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "CIAPI.Web.Controllers.InvestigatorsController", "Method": "GetDistinctRegions", "RelativePath": "api/Investigators/filters/regions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "CIAPI.Shared.DTOs.Common.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "CIAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "CIAPI.Web.Controllers.InvestigatorsController", "Method": "GetHealth", "RelativePath": "api/Investigators/health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Object", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "CIAPI.Web.Controllers.InvestigatorsController", "Method": "GetInvestigatorsByRegion", "RelativePath": "api/Investigators/region/{regionName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "regionName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "CIAPI.Shared.DTOs.Common.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[CIAPI.Modules.ProductManagement.Application.DTOs.InvestigatorDto, CIAPI.Modules.ProductManagement, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "CIAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "CIAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "CIAPI.Web.Controllers.InvestigatorsController", "Method": "GetInvestigatorsSample", "RelativePath": "api/Investigators/sample", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "CIAPI.Shared.DTOs.Common.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[CIAPI.Modules.ProductManagement.Application.DTOs.InvestigatorDto, CIAPI.Modules.ProductManagement, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "CIAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "CIAPI.Web.Controllers.InvestigatorsController", "Method": "SearchInvestigators", "RelativePath": "api/Investigators/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchTerm", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "CIAPI.Shared.DTOs.Common.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[CIAPI.Modules.ProductManagement.Application.DTOs.InvestigatorDto, CIAPI.Modules.ProductManagement, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "CIAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "CIAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_3", "RelativePath": "health", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "<>f__AnonymousType2`2[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["Health"], "EndpointName": "HealthCheck"}]