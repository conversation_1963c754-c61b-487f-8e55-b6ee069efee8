-- CI Solution Database Schema
-- This script creates the database schema for the CI Solution application
-- Database: CISolutionDB
-- Version: 1.0.0

USE master;
GO

-- Create database if it doesn't exist
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'CISolutionDB')
BEGIN
    CREATE DATABASE CISolutionDB;
END
GO

USE CISolutionDB;
GO

-- Create Users table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
BEGIN
    CREATE TABLE Users (
        Id int IDENTITY(1,1) PRIMARY KEY,
        FirstName nvarchar(100) NOT NULL,
        LastName nvarchar(100) NOT NULL,
        Email nvarchar(255) NOT NULL UNIQUE,
        PhoneNumber nvarchar(20) NOT NULL,
        PasswordHash nvarchar(max) NOT NULL,
        ProfileImageUrl nvarchar(500) NULL,
        LastLoginAt datetime2 NULL,
        EmailConfirmed bit NOT NULL DEFAULT 0,
        PhoneConfirmed bit NOT NULL DEFAULT 0,
        TwoFactorEnabled bit NOT NULL DEFAULT 0,
        AccessFailedCount int NOT NULL DEFAULT 0,
        LockoutEnd datetime2 NULL,
        LockoutEnabled bit NOT NULL DEFAULT 1,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL,
        CreatedBy nvarchar(100) NULL,
        UpdatedBy nvarchar(100) NULL,
        IsActive bit NOT NULL DEFAULT 1,
        IsDeleted bit NOT NULL DEFAULT 0,
        RowVersion rowversion
    );
    
    -- Create indexes
    CREATE INDEX IX_Users_Email ON Users(Email);
    CREATE INDEX IX_Users_PhoneNumber ON Users(PhoneNumber);
    CREATE INDEX IX_Users_IsActive ON Users(IsActive);
    CREATE INDEX IX_Users_IsDeleted ON Users(IsDeleted);
    CREATE INDEX IX_Users_CreatedAt ON Users(CreatedAt);
END
GO

-- Create Roles table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Roles' AND xtype='U')
BEGIN
    CREATE TABLE Roles (
        Id int IDENTITY(1,1) PRIMARY KEY,
        Name nvarchar(100) NOT NULL UNIQUE,
        NormalizedName nvarchar(100) NOT NULL UNIQUE,
        Description nvarchar(500) NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL,
        CreatedBy nvarchar(100) NULL,
        UpdatedBy nvarchar(100) NULL,
        IsActive bit NOT NULL DEFAULT 1,
        IsDeleted bit NOT NULL DEFAULT 0,
        RowVersion rowversion
    );
    
    -- Create indexes
    CREATE INDEX IX_Roles_Name ON Roles(Name);
    CREATE INDEX IX_Roles_NormalizedName ON Roles(NormalizedName);
    CREATE INDEX IX_Roles_IsActive ON Roles(IsActive);
    CREATE INDEX IX_Roles_IsDeleted ON Roles(IsDeleted);
END
GO

-- Create UserRoles junction table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='UserRoles' AND xtype='U')
BEGIN
    CREATE TABLE UserRoles (
        Id int IDENTITY(1,1) PRIMARY KEY,
        UserId int NOT NULL,
        RoleId int NOT NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL,
        CreatedBy nvarchar(100) NULL,
        UpdatedBy nvarchar(100) NULL,
        IsActive bit NOT NULL DEFAULT 1,
        IsDeleted bit NOT NULL DEFAULT 0,
        RowVersion rowversion,
        
        CONSTRAINT FK_UserRoles_Users FOREIGN KEY (UserId) REFERENCES Users(Id) ON DELETE CASCADE,
        CONSTRAINT FK_UserRoles_Roles FOREIGN KEY (RoleId) REFERENCES Roles(Id) ON DELETE CASCADE,
        CONSTRAINT UQ_UserRoles_User_Role UNIQUE (UserId, RoleId)
    );
    
    -- Create indexes
    CREATE INDEX IX_UserRoles_UserId ON UserRoles(UserId);
    CREATE INDEX IX_UserRoles_RoleId ON UserRoles(RoleId);
    CREATE INDEX IX_UserRoles_IsActive ON UserRoles(IsActive);
    CREATE INDEX IX_UserRoles_IsDeleted ON UserRoles(IsDeleted);
END
GO

-- Create Permissions table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Permissions' AND xtype='U')
BEGIN
    CREATE TABLE Permissions (
        Id int IDENTITY(1,1) PRIMARY KEY,
        Name nvarchar(100) NOT NULL,
        Description nvarchar(500) NULL,
        Module nvarchar(100) NOT NULL,
        Action nvarchar(50) NOT NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL,
        CreatedBy nvarchar(100) NULL,
        UpdatedBy nvarchar(100) NULL,
        IsActive bit NOT NULL DEFAULT 1,
        IsDeleted bit NOT NULL DEFAULT 0,
        RowVersion rowversion,
        
        CONSTRAINT UQ_Permissions_Name_Module_Action UNIQUE (Name, Module, Action)
    );
    
    -- Create indexes
    CREATE INDEX IX_Permissions_Name ON Permissions(Name);
    CREATE INDEX IX_Permissions_Module ON Permissions(Module);
    CREATE INDEX IX_Permissions_Action ON Permissions(Action);
    CREATE INDEX IX_Permissions_IsActive ON Permissions(IsActive);
    CREATE INDEX IX_Permissions_IsDeleted ON Permissions(IsDeleted);
END
GO

-- Create RolePermissions junction table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='RolePermissions' AND xtype='U')
BEGIN
    CREATE TABLE RolePermissions (
        Id int IDENTITY(1,1) PRIMARY KEY,
        RoleId int NOT NULL,
        PermissionId int NOT NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL,
        CreatedBy nvarchar(100) NULL,
        UpdatedBy nvarchar(100) NULL,
        IsActive bit NOT NULL DEFAULT 1,
        IsDeleted bit NOT NULL DEFAULT 0,
        RowVersion rowversion,
        
        CONSTRAINT FK_RolePermissions_Roles FOREIGN KEY (RoleId) REFERENCES Roles(Id) ON DELETE CASCADE,
        CONSTRAINT FK_RolePermissions_Permissions FOREIGN KEY (PermissionId) REFERENCES Permissions(Id) ON DELETE CASCADE,
        CONSTRAINT UQ_RolePermissions_Role_Permission UNIQUE (RoleId, PermissionId)
    );
    
    -- Create indexes
    CREATE INDEX IX_RolePermissions_RoleId ON RolePermissions(RoleId);
    CREATE INDEX IX_RolePermissions_PermissionId ON RolePermissions(PermissionId);
    CREATE INDEX IX_RolePermissions_IsActive ON RolePermissions(IsActive);
    CREATE INDEX IX_RolePermissions_IsDeleted ON RolePermissions(IsDeleted);
END
GO

-- Insert default roles
IF NOT EXISTS (SELECT * FROM Roles WHERE Name = 'SuperAdmin')
BEGIN
    INSERT INTO Roles (Name, NormalizedName, Description, CreatedBy)
    VALUES 
        ('SuperAdmin', 'SUPERADMIN', 'Super Administrator with full system access', 'System'),
        ('Admin', 'ADMIN', 'Administrator with management access', 'System'),
        ('Manager', 'MANAGER', 'Manager with limited administrative access', 'System'),
        ('User', 'USER', 'Regular user with basic access', 'System');
END
GO

-- Insert default permissions
IF NOT EXISTS (SELECT * FROM Permissions WHERE Name = 'Users.View')
BEGIN
    INSERT INTO Permissions (Name, Description, Module, Action, CreatedBy)
    VALUES 
        -- User Management Permissions
        ('Users.View', 'View users', 'UserManagement', 'View', 'System'),
        ('Users.Create', 'Create users', 'UserManagement', 'Create', 'System'),
        ('Users.Update', 'Update users', 'UserManagement', 'Update', 'System'),
        ('Users.Delete', 'Delete users', 'UserManagement', 'Delete', 'System'),
        ('Users.ManageRoles', 'Manage user roles', 'UserManagement', 'ManageRoles', 'System'),
        
        -- Role Management Permissions
        ('Roles.View', 'View roles', 'RoleManagement', 'View', 'System'),
        ('Roles.Create', 'Create roles', 'RoleManagement', 'Create', 'System'),
        ('Roles.Update', 'Update roles', 'RoleManagement', 'Update', 'System'),
        ('Roles.Delete', 'Delete roles', 'RoleManagement', 'Delete', 'System'),
        ('Roles.ManagePermissions', 'Manage role permissions', 'RoleManagement', 'ManagePermissions', 'System'),
        
        -- System Permissions
        ('System.ViewLogs', 'View system logs', 'System', 'ViewLogs', 'System'),
        ('System.ManageSettings', 'Manage system settings', 'System', 'ManageSettings', 'System'),
        ('System.ViewReports', 'View system reports', 'System', 'ViewReports', 'System');
END
GO

-- Assign permissions to SuperAdmin role
DECLARE @SuperAdminRoleId int = (SELECT Id FROM Roles WHERE Name = 'SuperAdmin');
IF @SuperAdminRoleId IS NOT NULL AND NOT EXISTS (SELECT * FROM RolePermissions WHERE RoleId = @SuperAdminRoleId)
BEGIN
    INSERT INTO RolePermissions (RoleId, PermissionId, CreatedBy)
    SELECT @SuperAdminRoleId, Id, 'System'
    FROM Permissions;
END
GO

-- Create default admin user (password: Admin@123)
IF NOT EXISTS (SELECT * FROM Users WHERE Email = '<EMAIL>')
BEGIN
    INSERT INTO Users (FirstName, LastName, Email, PhoneNumber, PasswordHash, EmailConfirmed, IsActive, CreatedBy)
    VALUES ('System', 'Administrator', '<EMAIL>', '+1234567890', 
            'AQAAAAEAACcQAAAAEJ4Q4Q4Q4Q4Q4Q4Q4Q4Q4Q4Q4Q4Q4Q4Q4Q4Q4Q4Q4Q4Q4Q4Q4Q4Q4Q4Q4Q4Q4Q==', 
            1, 1, 'System');
    
    -- Assign SuperAdmin role to default admin user
    DECLARE @AdminUserId int = SCOPE_IDENTITY();
    INSERT INTO UserRoles (UserId, RoleId, CreatedBy)
    VALUES (@AdminUserId, @SuperAdminRoleId, 'System');
END
GO

PRINT 'Database schema created successfully!';
PRINT 'Default admin user created: <EMAIL> (Password: Admin@123)';
GO
