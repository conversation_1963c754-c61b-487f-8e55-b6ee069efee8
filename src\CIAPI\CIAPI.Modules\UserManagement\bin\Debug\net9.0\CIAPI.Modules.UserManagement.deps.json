{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"CIAPI.Modules.UserManagement/1.0.0": {"dependencies": {"CIAPI.Core": "1.0.0", "CIAPI.Shared": "1.0.0"}, "runtime": {"CIAPI.Modules.UserManagement.dll": {}}}, "CIAPI.Core/1.0.0": {"runtime": {"CIAPI.Core.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "CIAPI.Shared/1.0.0": {"runtime": {"CIAPI.Shared.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"CIAPI.Modules.UserManagement/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CIAPI.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CIAPI.Shared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}