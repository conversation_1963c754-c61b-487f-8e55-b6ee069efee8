using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using System.Data;

namespace CIAPI.Modules.ProductManagement.Infrastructure.Data;

/// <summary>
/// SQL Server implementation of database connection factory for ProductManagement module
/// </summary>
public class ProductManagementSqlServerConnectionFactory : IProductManagementDbConnectionFactory
{
    private readonly string _connectionString;

    public ProductManagementSqlServerConnectionFactory(IConfiguration configuration)
    {
        _connectionString = configuration.GetConnectionString("DefaultConnection") 
            ?? throw new InvalidOperationException("DefaultConnection connection string is not configured");
    }

    public ProductManagementSqlServerConnectionFactory(string connectionString)
    {
        _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
    }

    public string ConnectionString => _connectionString;

    public IDbConnection CreateConnection()
    {
        return new SqlConnection(_connectionString);
    }

    public async Task<IDbConnection> CreateConnectionAsync()
    {
        var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();
        return connection;
    }
}
