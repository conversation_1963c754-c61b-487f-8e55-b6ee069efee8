using System.ComponentModel.DataAnnotations;

namespace CIAPI.Core.Entities;

/// <summary>
/// User entity representing system users
/// </summary>
public class User : BaseEntity
{
    [Required]
    [StringLength(100)]
    public string FirstName { get; set; } = string.Empty;
    
    [Required]
    [StringLength(100)]
    public string LastName { get; set; } = string.Empty;
    
    [Required]
    [EmailAddress]
    [StringLength(255)]
    public string Email { get; set; } = string.Empty;
    
    [Required]
    [StringLength(20)]
    public string PhoneNumber { get; set; } = string.Empty;
    
    [Required]
    public string PasswordHash { get; set; } = string.Empty;
    
    public string? ProfileImageUrl { get; set; }
    
    public DateTime? LastLoginAt { get; set; }
    
    public bool EmailConfirmed { get; set; } = false;
    
    public bool PhoneConfirmed { get; set; } = false;
    
    public bool TwoFactorEnabled { get; set; } = false;
    
    public int AccessFailedCount { get; set; } = 0;
    
    public DateTime? LockoutEnd { get; set; }
    
    public bool LockoutEnabled { get; set; } = true;
    
    // Navigation properties
    public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
    
    // Computed properties
    public string FullName => $"{FirstName} {LastName}";
    
    public bool IsLockedOut => LockoutEnd.HasValue && LockoutEnd > DateTime.UtcNow;
}
