using CIAPI.Modules.UserManagement.DTOs;
using CIAPI.Shared.DTOs.Common;

namespace CIAPI.Modules.UserManagement.Services;

/// <summary>
/// User service interface for user management operations
/// </summary>
public interface IUserService
{
    // User CRUD operations
    Task<ApiResponse<UserDto>> GetUserByIdAsync(int id, CancellationToken cancellationToken = default);
    Task<ApiResponse<UserDto>> GetUserByEmailAsync(string email, CancellationToken cancellationToken = default);
    Task<ApiResponse<PagedResult<UserDto>>> GetUsersAsync(PaginationParameters parameters, CancellationToken cancellationToken = default);
    Task<ApiResponse<UserDto>> CreateUserAsync(CreateUserRequest request, CancellationToken cancellationToken = default);
    Task<ApiResponse<UserDto>> UpdateUserAsync(int id, UpdateUserRequest request, CancellationToken cancellationToken = default);
    Task<ApiResponse> DeleteUserAsync(int id, CancellationToken cancellationToken = default);
    Task<ApiResponse> ActivateUserAsync(int id, CancellationToken cancellationToken = default);
    Task<ApiResponse> DeactivateUserAsync(int id, CancellationToken cancellationToken = default);
    
    // Authentication operations
    Task<ApiResponse<LoginResponse>> LoginAsync(LoginRequest request, CancellationToken cancellationToken = default);
    Task<ApiResponse> LogoutAsync(string userId, CancellationToken cancellationToken = default);
    Task<ApiResponse<LoginResponse>> RefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default);
    
    // Password operations
    Task<ApiResponse> ChangePasswordAsync(int userId, ChangePasswordRequest request, CancellationToken cancellationToken = default);
    Task<ApiResponse> ResetPasswordAsync(string email, CancellationToken cancellationToken = default);
    Task<ApiResponse> ConfirmPasswordResetAsync(string email, string token, string newPassword, CancellationToken cancellationToken = default);
    
    // Account verification
    Task<ApiResponse> SendEmailConfirmationAsync(int userId, CancellationToken cancellationToken = default);
    Task<ApiResponse> ConfirmEmailAsync(int userId, string token, CancellationToken cancellationToken = default);
    Task<ApiResponse> SendPhoneConfirmationAsync(int userId, CancellationToken cancellationToken = default);
    Task<ApiResponse> ConfirmPhoneAsync(int userId, string token, CancellationToken cancellationToken = default);
    
    // Role management
    Task<ApiResponse<IEnumerable<string>>> GetUserRolesAsync(int userId, CancellationToken cancellationToken = default);
    Task<ApiResponse> AssignRoleToUserAsync(int userId, string roleName, CancellationToken cancellationToken = default);
    Task<ApiResponse> RemoveRoleFromUserAsync(int userId, string roleName, CancellationToken cancellationToken = default);
    Task<ApiResponse> UpdateUserRolesAsync(int userId, IEnumerable<string> roleNames, CancellationToken cancellationToken = default);
    
    // Security operations
    Task<ApiResponse> LockUserAsync(int userId, DateTime? lockoutEnd = null, CancellationToken cancellationToken = default);
    Task<ApiResponse> UnlockUserAsync(int userId, CancellationToken cancellationToken = default);
    Task<ApiResponse> EnableTwoFactorAsync(int userId, CancellationToken cancellationToken = default);
    Task<ApiResponse> DisableTwoFactorAsync(int userId, CancellationToken cancellationToken = default);
    
    // Validation operations
    Task<ApiResponse<bool>> ValidateEmailAsync(string email, int? excludeUserId = null, CancellationToken cancellationToken = default);
    Task<ApiResponse<bool>> ValidatePhoneAsync(string phoneNumber, int? excludeUserId = null, CancellationToken cancellationToken = default);
}
