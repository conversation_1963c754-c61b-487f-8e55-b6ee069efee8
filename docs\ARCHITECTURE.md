# CI Solution - Architecture Overview

## System Architecture

CI Solution implements a **Modular Monolithic Architecture** that provides the benefits of microservices organization while maintaining the simplicity of a monolithic deployment.

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    CIWeb        │    │   CIDashboard   │    │     CIA<PERSON>       │
│   (Next.js)     │◄──►│  (ASP.NET MVC)  │◄──►│  (.NET Core)    │
│   Frontend      │    │   Dashboard     │    │   Web API       │
│   Port: 3000    │    │   Port: 5228    │    │   Port: 5199    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                               ┌─────────────────┐
                                               │   SQL Server    │
                                               │   Database      │
                                               │  (LocalDB)      │
                                               └─────────────────┘
```

## CIAPI - Centralized Web API

### Architecture Pattern: Clean Architecture + Modular Monolith

```
CIAPI/
├── CIAPI.Web/                 # Presentation Layer
│   ├── Controllers/           # API Controllers
│   ├── Program.cs            # Application startup
│   └── appsettings.json      # Configuration
├── CIAPI.Core/               # Domain Layer
│   ├── Entities/             # Domain entities
│   └── Interfaces/           # Repository interfaces
├── CIAPI.Infrastructure/     # Infrastructure Layer
│   ├── Data/                 # Database connections
│   └── Repositories/         # Data access implementations
├── CIAPI.Modules/           # Business Modules
│   ├── UserManagement/      # User domain module
│   └── ProductManagement/   # Product domain module
└── CIAPI.Shared/           # Shared Components
    └── DTOs/               # Data transfer objects
```

### Key Technologies
- **.NET 8.0** - Latest LTS framework
- **ADO.NET + Dapper** - High-performance data access
- **SQL Server** - Relational database
- **Swagger/OpenAPI** - API documentation
- **Repository Pattern** - Data access abstraction

### Design Principles
- **Single Responsibility** - Each module handles one business domain
- **Dependency Inversion** - Interfaces define contracts
- **Clean Architecture** - Clear separation of concerns
- **SOLID Principles** - Maintainable and extensible code

## CIDashboard - Administrative Interface

### Architecture Pattern: MVC with Service Layer

```
CIDashboard/
├── Controllers/              # MVC Controllers
├── Views/                   # Razor Views
├── Models/                  # View Models & DTOs
├── Services/               # API Communication
└── wwwroot/               # Static assets
```

### Key Features
- **ASP.NET MVC Core** - Server-side rendering
- **Bootstrap 5** - Responsive UI framework
- **HttpClient** - API communication
- **Razor Views** - Dynamic HTML generation
- **Session Management** - User state handling

### Responsibilities
- User management interface
- System administration
- API health monitoring
- Data visualization
- Administrative workflows

## CIWeb - User Frontend

### Architecture Pattern: JAMstack with API Integration

```
ciweb/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── page.tsx        # Home page
│   │   └── users/          # Users section
│   └── lib/                # Utilities
│       └── api.ts          # API service layer
├── public/                 # Static assets
└── package.json           # Dependencies
```

### Key Technologies
- **Next.js 15** - React framework with SSR/SSG
- **TypeScript** - Type-safe JavaScript
- **Tailwind CSS** - Utility-first CSS framework
- **React Hooks** - Modern React patterns
- **Fetch API** - HTTP client for API calls

### Features
- Server-side rendering (SSR)
- Static site generation (SSG)
- Responsive design
- Real-time API status
- Modern user experience

## Data Architecture

### Database Design

```sql
Users
├── Id (PK)
├── FirstName, LastName
├── Email (Unique)
├── PhoneNumber
├── PasswordHash
├── Security fields (2FA, Lockout)
└── Audit fields (Created, Updated)

Roles
├── Id (PK)
├── Name (Unique)
├── NormalizedName
└── Description

UserRoles (Junction Table)
├── UserId (FK)
├── RoleId (FK)
└── Audit fields

Permissions
├── Id (PK)
├── Name
├── Module
└── Action

RolePermissions (Junction Table)
├── RoleId (FK)
├── PermissionId (FK)
└── Audit fields
```

### Data Access Strategy
- **Repository Pattern** - Abstraction over data access
- **ADO.NET + Dapper** - High-performance ORM
- **Connection Factory** - Centralized connection management
- **Stored Procedures** - Complex business logic
- **Optimistic Concurrency** - Row versioning

## Communication Patterns

### API Communication
- **RESTful APIs** - Standard HTTP methods
- **JSON Serialization** - Data exchange format
- **CORS Configuration** - Cross-origin requests
- **Error Handling** - Consistent error responses
- **Health Checks** - System monitoring

### Response Format
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { /* response data */ },
  "errors": null,
  "timestamp": "2025-07-11T07:00:00Z",
  "traceId": "abc123"
}
```

## Security Architecture

### Current Implementation
- **Input Validation** - Model validation attributes
- **SQL Injection Prevention** - Parameterized queries
- **CORS Policy** - Controlled cross-origin access
- **HTTPS Redirection** - Secure communication

### Planned Security Features
- **JWT Authentication** - Stateless authentication
- **Role-based Authorization** - Granular permissions
- **Rate Limiting** - API protection
- **Audit Logging** - Security monitoring

## Scalability Considerations

### Horizontal Scaling
- **Stateless API** - No server-side sessions
- **Database Connection Pooling** - Efficient resource usage
- **Caching Strategy** - Redis for distributed caching
- **Load Balancing** - Multiple API instances

### Vertical Scaling
- **Async/Await** - Non-blocking operations
- **Connection Pooling** - Database efficiency
- **Memory Management** - Proper disposal patterns
- **Performance Monitoring** - Application insights

## Development Standards

### Code Quality
- **Clean Code** - Readable and maintainable
- **SOLID Principles** - Object-oriented design
- **DRY Principle** - Don't repeat yourself
- **Unit Testing** - Comprehensive test coverage
- **Code Reviews** - Peer validation

### Documentation
- **API Documentation** - Swagger/OpenAPI
- **Code Comments** - Inline documentation
- **Architecture Docs** - System overview
- **Setup Guides** - Developer onboarding

## Deployment Architecture

### Development Environment
- **Local Development** - All services on localhost
- **Hot Reload** - Instant code changes
- **Debug Support** - Full debugging capabilities
- **Local Database** - SQL Server LocalDB

### Production Considerations
- **Container Deployment** - Docker support
- **Environment Configuration** - Separate configs
- **Health Monitoring** - Application insights
- **Backup Strategy** - Database backups
- **CI/CD Pipeline** - Automated deployment

## Technology Stack Summary

| Layer | Technology | Purpose |
|-------|------------|---------|
| **Frontend** | Next.js 15 + TypeScript | Modern React framework |
| **Dashboard** | ASP.NET MVC Core | Administrative interface |
| **API** | .NET 8.0 Web API | Centralized business logic |
| **Data Access** | ADO.NET + Dapper | High-performance ORM |
| **Database** | SQL Server | Relational data storage |
| **Styling** | Tailwind CSS + Bootstrap | Responsive design |
| **Documentation** | Swagger/OpenAPI | API documentation |

## Benefits of This Architecture

1. **Maintainability** - Clear separation of concerns
2. **Scalability** - Modular design allows independent scaling
3. **Testability** - Dependency injection enables unit testing
4. **Flexibility** - Easy to add new modules and features
5. **Performance** - Optimized data access and caching
6. **Developer Experience** - Modern tooling and hot reload
7. **Enterprise Ready** - Security, monitoring, and audit trails

---

This architecture provides a solid foundation for enterprise applications while maintaining modern development practices and scalability options.
