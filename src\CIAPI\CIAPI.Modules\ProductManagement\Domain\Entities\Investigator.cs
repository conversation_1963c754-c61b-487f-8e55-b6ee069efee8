using System.ComponentModel.DataAnnotations;

namespace CIAPI.Modules.ProductManagement.Domain.Entities;

/// <summary>
/// Base entity class that provides common properties for all entities
/// </summary>
public abstract class BaseEntity
{
    [Key]
    public int Id { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }
    
    public string? CreatedBy { get; set; }
    
    public string? UpdatedBy { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    public bool IsDeleted { get; set; } = false;
    
    /// <summary>
    /// Row version for optimistic concurrency control
    /// </summary>
    [Timestamp]
    public byte[]? RowVersion { get; set; }
}

/// <summary>
/// Investigator entity representing investigators in the system
/// </summary>
public class Investigator : BaseEntity
{
    [Required]
    [StringLength(200)]
    public string InvestigatorName { get; set; } = string.Empty;
    
    [StringLength(500)]
    public string? SpecializationNames { get; set; }
    
    [StringLength(100)]
    public string? Designation { get; set; }
    
    [StringLength(200)]
    public string? Organisation { get; set; }
    
    [StringLength(20)]
    public string? ContactNumber { get; set; }
    
    [EmailAddress]
    [StringLength(255)]
    public string? EmailID { get; set; }
    
    [StringLength(20)]
    public string? Fax { get; set; }
    
    [StringLength(100)]
    public string? RegionName { get; set; }
    
    [StringLength(100)]
    public string? CountryName { get; set; }
    
    [StringLength(100)]
    public string? StateName { get; set; }
    
    [StringLength(100)]
    public string? CityName { get; set; }
    
    // Computed properties for display
    public string FullLocation => $"{CityName}, {StateName}, {CountryName}".Trim(' ', ',');
    
    public string ContactInfo => !string.IsNullOrEmpty(ContactNumber) && !string.IsNullOrEmpty(EmailID) 
        ? $"{ContactNumber} | {EmailID}" 
        : ContactNumber ?? EmailID ?? "N/A";
}
