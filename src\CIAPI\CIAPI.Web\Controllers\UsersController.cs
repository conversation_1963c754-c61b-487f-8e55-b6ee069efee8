using Microsoft.AspNetCore.Mvc;
using CIAPI.Shared.DTOs.Common;
using CIAPI.Modules.UserManagement.DTOs;
using CIAPI.Core.Interfaces;

namespace CIAPI.Web.Controllers;

/// <summary>
/// Users management controller
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class UsersController : ControllerBase
{
    private readonly IUserRepository _userRepository;
    private readonly ILogger<UsersController> _logger;

    public UsersController(IUserRepository userRepository, ILogger<UsersController> logger)
    {
        _userRepository = userRepository ?? throw new ArgumentNullException(nameof(userRepository));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Get all users with pagination
    /// </summary>
    /// <param name="pageNumber">Page number (default: 1)</param>
    /// <param name="pageSize">Page size (default: 10, max: 100)</param>
    /// <param name="searchTerm">Search term for filtering users</param>
    /// <returns>Paginated list of users</returns>
    [HttpGet]
    [ProducesResponseType(typeof(ApiResponse<PagedResult<UserDto>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<PagedResult<UserDto>>>> GetUsers(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? searchTerm = null)
    {
        try
        {
            _logger.LogInformation("Getting users with pagination. Page: {PageNumber}, Size: {PageSize}, Search: {SearchTerm}", 
                pageNumber, pageSize, searchTerm);

            // Validate pagination parameters
            if (pageNumber < 1)
                return BadRequest(ApiResponse<PagedResult<UserDto>>.ErrorResult("Page number must be greater than 0"));

            if (pageSize < 1 || pageSize > 100)
                return BadRequest(ApiResponse<PagedResult<UserDto>>.ErrorResult("Page size must be between 1 and 100"));

            // For now, return a sample response since we haven't implemented the full service layer
            var sampleUsers = new List<UserDto>
            {
                new UserDto
                {
                    Id = 1,
                    FirstName = "John",
                    LastName = "Doe",
                    FullName = "John Doe",
                    Email = "<EMAIL>",
                    PhoneNumber = "+1234567890",
                    EmailConfirmed = true,
                    PhoneConfirmed = false,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow.AddDays(-30),
                    Roles = new[] { "User" }
                },
                new UserDto
                {
                    Id = 2,
                    FirstName = "Jane",
                    LastName = "Smith",
                    FullName = "Jane Smith",
                    Email = "<EMAIL>",
                    PhoneNumber = "+1234567891",
                    EmailConfirmed = true,
                    PhoneConfirmed = true,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow.AddDays(-15),
                    Roles = new[] { "User", "Admin" }
                }
            };

            var pagedResult = new PagedResult<UserDto>(
                sampleUsers.Skip((pageNumber - 1) * pageSize).Take(pageSize),
                pageNumber,
                pageSize,
                sampleUsers.Count
            );

            return Ok(ApiResponse<PagedResult<UserDto>>.SuccessResult(pagedResult, "Users retrieved successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting users");
            return StatusCode(500, ApiResponse<PagedResult<UserDto>>.ErrorResult("An error occurred while retrieving users"));
        }
    }

    /// <summary>
    /// Get user by ID
    /// </summary>
    /// <param name="id">User ID</param>
    /// <returns>User details</returns>
    [HttpGet("{id:int}")]
    [ProducesResponseType(typeof(ApiResponse<UserDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<UserDto>>> GetUser(int id)
    {
        try
        {
            _logger.LogInformation("Getting user with ID: {UserId}", id);

            if (id <= 0)
                return BadRequest(ApiResponse<UserDto>.ErrorResult("Invalid user ID"));

            // For now, return a sample user
            if (id == 1)
            {
                var sampleUser = new UserDto
                {
                    Id = 1,
                    FirstName = "John",
                    LastName = "Doe",
                    FullName = "John Doe",
                    Email = "<EMAIL>",
                    PhoneNumber = "+1234567890",
                    EmailConfirmed = true,
                    PhoneConfirmed = false,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow.AddDays(-30),
                    Roles = new[] { "User" }
                };

                return Ok(ApiResponse<UserDto>.SuccessResult(sampleUser, "User retrieved successfully"));
            }

            return NotFound(ApiResponse<UserDto>.ErrorResult("User not found"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting user with ID: {UserId}", id);
            return StatusCode(500, ApiResponse<UserDto>.ErrorResult("An error occurred while retrieving the user"));
        }
    }

    /// <summary>
    /// Create a new user
    /// </summary>
    /// <param name="request">User creation request</param>
    /// <returns>Created user details</returns>
    [HttpPost]
    [ProducesResponseType(typeof(ApiResponse<UserDto>), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<UserDto>>> CreateUser([FromBody] CreateUserRequest request)
    {
        try
        {
            _logger.LogInformation("Creating new user with email: {Email}", request.Email);

            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values
                    .SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage);
                return BadRequest(ApiResponse<UserDto>.ErrorResult(errors));
            }

            // For now, return a sample created user
            var createdUser = new UserDto
            {
                Id = new Random().Next(1000, 9999),
                FirstName = request.FirstName,
                LastName = request.LastName,
                FullName = $"{request.FirstName} {request.LastName}",
                Email = request.Email,
                PhoneNumber = request.PhoneNumber,
                ProfileImageUrl = request.ProfileImageUrl,
                EmailConfirmed = request.EmailConfirmed,
                PhoneConfirmed = request.PhoneConfirmed,
                IsActive = request.IsActive,
                CreatedAt = DateTime.UtcNow,
                Roles = request.Roles
            };

            return CreatedAtAction(
                nameof(GetUser),
                new { id = createdUser.Id },
                ApiResponse<UserDto>.SuccessResult(createdUser, "User created successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while creating user");
            return StatusCode(500, ApiResponse<UserDto>.ErrorResult("An error occurred while creating the user"));
        }
    }

    /// <summary>
    /// Health check for Users module
    /// </summary>
    /// <returns>Health status</returns>
    [HttpGet("health")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    public ActionResult GetHealth()
    {
        return Ok(new
        {
            Module = "UserManagement",
            Status = "Healthy",
            Timestamp = DateTime.UtcNow,
            Version = "1.0.0"
        });
    }
}
