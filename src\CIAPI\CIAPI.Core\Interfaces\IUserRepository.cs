using CIAPI.Core.Entities;

namespace CIAPI.Core.Interfaces;

/// <summary>
/// User-specific repository interface with additional user operations
/// </summary>
public interface IUserRepository : IRepository<User>
{
    Task<User?> GetByEmailAsync(string email, CancellationToken cancellationToken = default);
    Task<User?> GetByPhoneNumberAsync(string phoneNumber, CancellationToken cancellationToken = default);
    Task<bool> EmailExistsAsync(string email, int? excludeUserId = null, CancellationToken cancellationToken = default);
    Task<bool> PhoneExistsAsync(string phoneNumber, int? excludeUserId = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<User>> GetUsersByRoleAsync(string roleName, CancellationToken cancellationToken = default);
    Task<IEnumerable<string>> GetUserRolesAsync(int userId, CancellationToken cancellationToken = default);
    Task<IEnumerable<string>> GetUserPermissionsAsync(int userId, CancellationToken cancellationToken = default);
    Task<User?> GetUserWithRolesAsync(int userId, CancellationToken cancellationToken = default);
    Task UpdateLastLoginAsync(int userId, CancellationToken cancellationToken = default);
    Task IncrementAccessFailedCountAsync(int userId, CancellationToken cancellationToken = default);
    Task ResetAccessFailedCountAsync(int userId, CancellationToken cancellationToken = default);
    Task SetLockoutAsync(int userId, DateTime? lockoutEnd, CancellationToken cancellationToken = default);
}

/// <summary>
/// Role-specific repository interface
/// </summary>
public interface IRoleRepository : IRepository<Role>
{
    Task<Role?> GetByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<bool> RoleExistsAsync(string name, int? excludeRoleId = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<Role>> GetRolesWithPermissionsAsync(CancellationToken cancellationToken = default);
    Task<Role?> GetRoleWithPermissionsAsync(int roleId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Permission>> GetRolePermissionsAsync(int roleId, CancellationToken cancellationToken = default);
    Task AddPermissionToRoleAsync(int roleId, int permissionId, CancellationToken cancellationToken = default);
    Task RemovePermissionFromRoleAsync(int roleId, int permissionId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Permission-specific repository interface
/// </summary>
public interface IPermissionRepository : IRepository<Permission>
{
    Task<Permission?> GetByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<IEnumerable<Permission>> GetByModuleAsync(string module, CancellationToken cancellationToken = default);
    Task<bool> PermissionExistsAsync(string name, string module, string action, int? excludePermissionId = null, CancellationToken cancellationToken = default);
}
