using CIAPI.Core.Entities;
using CIAPI.Core.Interfaces;
using CIAPI.Infrastructure.Data;
using Dapper;
using System.Data;
using System.Linq.Expressions;
using System.Text;

namespace CIAPI.Infrastructure.Repositories;

/// <summary>
/// Base repository implementation using ADO.NET with Dapper
/// </summary>
/// <typeparam name="T">Entity type that inherits from BaseEntity</typeparam>
public abstract class BaseRepository<T> : IRepository<T> where T : BaseEntity
{
    protected readonly IDbConnectionFactory _connectionFactory;
    protected readonly string _tableName;

    protected BaseRepository(IDbConnectionFactory connectionFactory, string tableName)
    {
        _connectionFactory = connectionFactory ?? throw new ArgumentNullException(nameof(connectionFactory));
        _tableName = tableName ?? throw new ArgumentNullException(nameof(tableName));
    }

    public virtual async Task<T?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = $"SELECT * FROM {_tableName} WHERE Id = @Id AND IsDeleted = 0";
        return await connection.QueryFirstOrDefaultAsync<T>(sql, new { Id = id });
    }

    public virtual async Task<IEnumerable<T>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = $"SELECT * FROM {_tableName} WHERE IsDeleted = 0 ORDER BY CreatedAt DESC";
        return await connection.QueryAsync<T>(sql);
    }

    public virtual async Task<T> AddAsync(T entity, CancellationToken cancellationToken = default)
    {
        entity.CreatedAt = DateTime.UtcNow;
        entity.IsActive = true;
        entity.IsDeleted = false;

        using var connection = _connectionFactory.CreateConnection();
        var insertSql = GenerateInsertSql();
        var id = await connection.QuerySingleAsync<int>(insertSql, entity);
        entity.Id = id;
        return entity;
    }

    public virtual async Task<T> UpdateAsync(T entity, CancellationToken cancellationToken = default)
    {
        entity.UpdatedAt = DateTime.UtcNow;

        using var connection = _connectionFactory.CreateConnection();
        var updateSql = GenerateUpdateSql();
        await connection.ExecuteAsync(updateSql, entity);
        return entity;
    }

    public virtual async Task DeleteAsync(int id, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = $"DELETE FROM {_tableName} WHERE Id = @Id";
        await connection.ExecuteAsync(sql, new { Id = id });
    }

    public virtual async Task DeleteAsync(T entity, CancellationToken cancellationToken = default)
    {
        await DeleteAsync(entity.Id, cancellationToken);
    }

    public virtual async Task SoftDeleteAsync(int id, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = $"UPDATE {_tableName} SET IsDeleted = 1, UpdatedAt = @UpdatedAt WHERE Id = @Id";
        await connection.ExecuteAsync(sql, new { Id = id, UpdatedAt = DateTime.UtcNow });
    }

    public virtual async Task SoftDeleteAsync(T entity, CancellationToken cancellationToken = default)
    {
        await SoftDeleteAsync(entity.Id, cancellationToken);
    }

    public virtual async Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        var count = await CountAsync(predicate, cancellationToken);
        return count > 0;
    }

    public virtual async Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = $"SELECT COUNT(*) FROM {_tableName} WHERE IsDeleted = 0";
        
        if (predicate != null)
        {
            // Note: For full implementation, you'd need expression tree to SQL conversion
            // For now, this is a simplified version
            sql += " AND " + ConvertExpressionToSql(predicate);
        }
        
        return await connection.QuerySingleAsync<int>(sql);
    }

    // Abstract methods to be implemented by derived classes
    protected abstract string GenerateInsertSql();
    protected abstract string GenerateUpdateSql();
    protected abstract string ConvertExpressionToSql(Expression<Func<T, bool>> predicate);

    // Not implemented in base class - would need expression tree parsing
    public virtual Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("FindAsync with expression requires custom implementation in derived class");
    }

    public virtual Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("FirstOrDefaultAsync with expression requires custom implementation in derived class");
    }

    public virtual Task<(IEnumerable<T> Items, int TotalCount)> GetPagedAsync(int pageNumber, int pageSize, Expression<Func<T, bool>>? predicate = null, Expression<Func<T, object>>? orderBy = null, bool ascending = true, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("GetPagedAsync with expressions requires custom implementation in derived class");
    }

    public virtual Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("AddRangeAsync requires custom implementation in derived class");
    }

    public virtual Task<IEnumerable<T>> UpdateRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("UpdateRangeAsync requires custom implementation in derived class");
    }

    public virtual Task DeleteRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("DeleteRangeAsync requires custom implementation in derived class");
    }

    public virtual Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // ADO.NET doesn't have a change tracker, so this is not applicable
        return Task.FromResult(0);
    }
}
