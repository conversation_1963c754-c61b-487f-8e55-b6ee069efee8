using CIAPI.Modules.ProductManagement.DTOs;
using CIAPI.Shared.DTOs.Common;

namespace CIAPI.Modules.ProductManagement.Services;

/// <summary>
/// Investigator service interface for investigator management operations
/// </summary>
public interface IInvestigatorService
{
    // Core CRUD operations
    Task<ApiResponse<InvestigatorDto>> GetInvestigatorByIdAsync(int id, CancellationToken cancellationToken = default);
    Task<ApiResponse<PagedResult<InvestigatorDto>>> GetInvestigatorsAsync(PaginationParameters parameters, CancellationToken cancellationToken = default);
    Task<ApiResponse<InvestigatorDto>> CreateInvestigatorAsync(CreateInvestigatorRequest request, CancellationToken cancellationToken = default);
    Task<ApiResponse<InvestigatorDto>> UpdateInvestigatorAsync(int id, UpdateInvestigatorRequest request, CancellationToken cancellationToken = default);
    Task<ApiResponse> DeleteInvestigatorAsync(int id, CancellationToken cancellationToken = default);
    Task<ApiResponse> ActivateInvestigatorAsync(int id, CancellationToken cancellationToken = default);
    Task<ApiResponse> DeactivateInvestigatorAsync(int id, CancellationToken cancellationToken = default);
    
    // Stored procedure operations
    Task<ApiResponse<IEnumerable<InvestigatorDto>>> GetInvestigatorsSampleAsync(CancellationToken cancellationToken = default);
    Task<ApiResponse<PagedResult<InvestigatorDto>>> GetInvestigatorsSamplePagedAsync(InvestigatorSearchRequest request, CancellationToken cancellationToken = default);
    
    // Search and filter operations
    Task<ApiResponse<IEnumerable<InvestigatorDto>>> SearchInvestigatorsByNameAsync(string searchTerm, CancellationToken cancellationToken = default);
    Task<ApiResponse<IEnumerable<InvestigatorDto>>> GetInvestigatorsByRegionAsync(string regionName, CancellationToken cancellationToken = default);
    Task<ApiResponse<IEnumerable<InvestigatorDto>>> GetInvestigatorsByCountryAsync(string countryName, CancellationToken cancellationToken = default);
    Task<ApiResponse<IEnumerable<InvestigatorDto>>> GetInvestigatorsBySpecializationAsync(string specialization, CancellationToken cancellationToken = default);
    Task<ApiResponse<IEnumerable<InvestigatorDto>>> GetInvestigatorsByOrganisationAsync(string organisation, CancellationToken cancellationToken = default);
    
    // Filter data operations
    Task<ApiResponse<InvestigatorFiltersDto>> GetInvestigatorFiltersAsync(CancellationToken cancellationToken = default);
    Task<ApiResponse<IEnumerable<string>>> GetDistinctRegionsAsync(CancellationToken cancellationToken = default);
    Task<ApiResponse<IEnumerable<string>>> GetDistinctCountriesAsync(CancellationToken cancellationToken = default);
    Task<ApiResponse<IEnumerable<string>>> GetDistinctSpecializationsAsync(CancellationToken cancellationToken = default);
    
    // Statistics operations
    Task<ApiResponse<InvestigatorStatsDto>> GetInvestigatorStatsAsync(CancellationToken cancellationToken = default);
    
    // Validation operations
    Task<ApiResponse<bool>> ValidateInvestigatorEmailAsync(string email, int? excludeInvestigatorId = null, CancellationToken cancellationToken = default);
    Task<ApiResponse<bool>> ValidateInvestigatorContactAsync(string contactNumber, int? excludeInvestigatorId = null, CancellationToken cancellationToken = default);
}
