# Investigators Module - Implementation Guide

## Overview

The Investigators module has been successfully implemented in the **CIAPI.Modules.ProductManagement** namespace, featuring the **GetInvestigators_Sample** stored procedure integration with full async implementation and centralized API connectivity.

## 🎯 Key Features Implemented

### ✅ Database Layer
- **Investigators Table** with all required columns
- **GetInvestigators_Sample Stored Procedure** 
- **Sample Data** with 10 test investigators
- **Proper Indexing** for performance optimization

### ✅ Repository Layer (ADO.NET + Dapper)
- **IInvestigatorRepository** interface with comprehensive methods
- **InvestigatorRepository** implementation with async operations
- **Stored Procedure Integration** using Dapper
- **Advanced Filtering** and search capabilities

### ✅ Service Layer
- **IInvestigatorService** interface with business logic
- **InvestigatorService** implementation with error handling
- **DTO Mapping** between entities and API responses
- **Comprehensive Logging** for debugging and monitoring

### ✅ API Layer
- **InvestigatorsController** with RESTful endpoints
- **Swagger Documentation** for all endpoints
- **Proper Error Handling** with consistent responses
- **Input Validation** using data annotations

## 📊 Database Schema

### Investigators Table Structure
```sql
CREATE TABLE Investigators (
    Id int IDENTITY(1,1) PRIMARY KEY,
    InvestigatorName nvarchar(200) NOT NULL,
    SpecializationNames nvarchar(500) NULL,
    Designation nvarchar(100) NULL,
    Organisation nvarchar(200) NULL,
    ContactNumber nvarchar(20) NULL,
    EmailID nvarchar(255) NULL,
    Fax nvarchar(20) NULL,
    RegionName nvarchar(100) NULL,
    CountryName nvarchar(100) NULL,
    StateName nvarchar(100) NULL,
    CityName nvarchar(100) NULL,
    -- Audit fields
    CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedAt datetime2 NULL,
    CreatedBy nvarchar(100) NULL,
    UpdatedBy nvarchar(100) NULL,
    IsActive bit NOT NULL DEFAULT 1,
    IsDeleted bit NOT NULL DEFAULT 0,
    RowVersion rowversion
);
```

### GetInvestigators_Sample Stored Procedure
```sql
CREATE PROCEDURE GetInvestigators_Sample
AS
BEGIN
    SELECT 
        InvestigatorName,
        SpecializationNames,
        Designation,
        Organisation,
        ContactNumber,
        EmailID,
        Fax,
        RegionName,
        CountryName,
        StateName,
        CityName
    FROM Investigators
    WHERE IsDeleted = 0
    ORDER BY InvestigatorName;
END
```

## 🔗 API Endpoints

### Base URL: `http://localhost:5199/api/investigators`

| Method | Endpoint | Description | Parameters |
|--------|----------|-------------|------------|
| **GET** | `/` | Get paginated investigators | `pageNumber`, `pageSize`, `searchTerm`, `regionFilter`, `countryFilter` |
| **GET** | `/sample` | Get all investigators from SP | None |
| **GET** | `/{id}` | Get investigator by ID | `id` (path parameter) |
| **POST** | `/` | Create new investigator | Request body with investigator data |
| **GET** | `/search` | Search investigators by name | `searchTerm` (query parameter) |
| **GET** | `/region/{regionName}` | Get investigators by region | `regionName` (path parameter) |
| **GET** | `/filters/regions` | Get distinct regions | None |
| **GET** | `/filters/countries` | Get distinct countries | None |
| **GET** | `/filters` | Get all filter options | None |
| **GET** | `/health` | Module health check | None |

## 📝 Sample API Responses

### Get Investigators (Paginated)
```json
{
  "success": true,
  "message": "Investigators retrieved successfully",
  "data": {
    "items": [
      {
        "id": 1,
        "investigatorName": "Dr. John Smith",
        "specializationNames": "Cardiology, Internal Medicine",
        "designation": "Senior Consultant",
        "organisation": "City General Hospital",
        "contactNumber": "+1-555-0101",
        "emailID": "<EMAIL>",
        "fax": "+1-555-0102",
        "regionName": "North America",
        "countryName": "United States",
        "stateName": "California",
        "cityName": "Los Angeles",
        "isActive": true,
        "createdAt": "2025-07-11T10:00:00Z",
        "fullLocation": "Los Angeles, California, United States",
        "contactInfo": "+1-555-0101 | <EMAIL>"
      }
    ],
    "pageNumber": 1,
    "pageSize": 10,
    "totalCount": 10,
    "totalPages": 1,
    "hasPreviousPage": false,
    "hasNextPage": false
  },
  "timestamp": "2025-07-11T10:00:00Z"
}
```

### Get Distinct Regions
```json
{
  "success": true,
  "message": "Regions retrieved successfully",
  "data": [
    "Asia Pacific",
    "Europe", 
    "North America"
  ],
  "timestamp": "2025-07-11T10:00:00Z"
}
```

## 🧪 Testing the Implementation

### 1. Health Check
```bash
curl -X GET "http://localhost:5199/api/investigators/health"
```

### 2. Get All Investigators (Stored Procedure)
```bash
curl -X GET "http://localhost:5199/api/investigators/sample"
```

### 3. Get Paginated Investigators
```bash
curl -X GET "http://localhost:5199/api/investigators?pageNumber=1&pageSize=5"
```

### 4. Search Investigators
```bash
curl -X GET "http://localhost:5199/api/investigators/search?searchTerm=John"
```

### 5. Filter by Region
```bash
curl -X GET "http://localhost:5199/api/investigators/region/North%20America"
```

### 6. Get Filter Options
```bash
curl -X GET "http://localhost:5199/api/investigators/filters/regions"
```

## 🏗️ Architecture Implementation

### Modular Monolithic Structure
```
CIAPI.Modules.ProductManagement/
├── DTOs/
│   └── InvestigatorDto.cs          # Data transfer objects
├── Services/
│   ├── IInvestigatorService.cs     # Service interface
│   └── InvestigatorService.cs      # Service implementation
└── (Controllers in CIAPI.Web)

CIAPI.Core/
├── Entities/
│   └── Investigator.cs             # Domain entity
└── Interfaces/
    └── IInvestigatorRepository.cs  # Repository interface

CIAPI.Infrastructure/
└── Repositories/
    └── InvestigatorRepository.cs   # Repository implementation

CIAPI.Web/
└── Controllers/
    └── InvestigatorsController.cs  # API controller
```

### Dependency Injection Configuration
```csharp
// In Program.cs
builder.Services.AddScoped<IInvestigatorRepository, InvestigatorRepository>();
builder.Services.AddScoped<IInvestigatorService, InvestigatorService>();
```

## 🔧 Key Implementation Details

### 1. Async Implementation
- All methods use `async/await` pattern
- Proper `CancellationToken` support
- Non-blocking database operations

### 2. Error Handling
- Try-catch blocks in all service methods
- Consistent error responses
- Comprehensive logging

### 3. Data Access Strategy
- ADO.NET with Dapper for performance
- Stored procedure integration
- In-memory filtering for complex queries

### 4. API Design
- RESTful endpoints
- Consistent response format
- Proper HTTP status codes
- Swagger documentation

## 📈 Performance Considerations

- **Indexed Columns**: InvestigatorName, RegionName, CountryName, Organisation
- **Efficient Queries**: Direct stored procedure calls
- **Pagination**: Server-side pagination to handle large datasets
- **Caching Ready**: Service layer designed for future caching implementation

## 🔒 Security Features

- **Input Validation**: Data annotations on DTOs
- **SQL Injection Prevention**: Parameterized queries with Dapper
- **Error Handling**: No sensitive information in error responses

## 🚀 Next Steps

1. **Authentication**: Add JWT authentication to secure endpoints
2. **Authorization**: Implement role-based access control
3. **Caching**: Add Redis caching for frequently accessed data
4. **Unit Tests**: Implement comprehensive test coverage
5. **Integration Tests**: Test stored procedure integration
6. **Performance Monitoring**: Add application insights

---

**✅ Implementation Status: COMPLETE**

The GetInvestigators_Sample stored procedure has been successfully integrated into the CIAPI.Modules.ProductManagement module with full async implementation and centralized API connectivity. All endpoints are working and tested successfully!
