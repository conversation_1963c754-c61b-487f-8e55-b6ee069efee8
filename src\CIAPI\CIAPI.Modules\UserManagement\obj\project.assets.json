{"version": 3, "targets": {"net9.0": {"CIAPI.Core/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "compile": {"bin/placeholder/CIAPI.Core.dll": {}}, "runtime": {"bin/placeholder/CIAPI.Core.dll": {}}}, "CIAPI.Shared/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "compile": {"bin/placeholder/CIAPI.Shared.dll": {}}, "runtime": {"bin/placeholder/CIAPI.Shared.dll": {}}}}}, "libraries": {"CIAPI.Core/1.0.0": {"type": "project", "path": "../../CIAPI.Core/CIAPI.Core.csproj", "msbuildProject": "../../CIAPI.Core/CIAPI.Core.csproj"}, "CIAPI.Shared/1.0.0": {"type": "project", "path": "../../CIAPI.Shared/CIAPI.Shared.csproj", "msbuildProject": "../../CIAPI.Shared/CIAPI.Shared.csproj"}}, "projectFileDependencyGroups": {"net9.0": ["CIAPI.Core >= 1.0.0", "CIAPI.Shared >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Modules\\UserManagement\\CIAPI.Modules.UserManagement.csproj", "projectName": "CIAPI.Modules.UserManagement", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Modules\\UserManagement\\CIAPI.Modules.UserManagement.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Modules\\UserManagement\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Core\\CIAPI.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Core\\CIAPI.Core.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Shared\\CIAPI.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Shared\\CIAPI.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}