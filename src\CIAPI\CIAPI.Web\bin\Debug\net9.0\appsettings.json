{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=CISolutionDB;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "JwtSettings": {"SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "CISolution", "Audience": "CISolution", "ExpirationInMinutes": 60, "RefreshTokenExpirationInDays": 7}, "EmailSettings": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "SenderEmail": "<EMAIL>", "SenderName": "CI Solution", "Username": "", "Password": "", "EnableSsl": true}, "SmsSettings": {"Provider": "<PERSON><PERSON><PERSON>", "AccountSid": "", "AuthToken": "", "FromPhoneNumber": ""}, "ApplicationSettings": {"ApplicationName": "CI Solution", "ApplicationVersion": "1.0.0", "Environment": "Development", "EnableDetailedErrors": true, "MaxLoginAttempts": 5, "LockoutDurationInMinutes": 30}}