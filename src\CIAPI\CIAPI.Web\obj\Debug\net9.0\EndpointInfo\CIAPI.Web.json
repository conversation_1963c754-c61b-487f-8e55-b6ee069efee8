{"openapi": "3.0.4", "info": {"title": "CI API", "description": "Centralized API for CI Solution - A Modern Modular Monolithic Architecture", "contact": {"name": "CI Solution Team", "email": "<EMAIL>"}, "version": "v1"}, "paths": {"/health": {"get": {"tags": ["Health"], "operationId": "HealthCheck", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StringDateTime<>f__AnonymousType2"}}}}}}}, "/api/Investigators": {"get": {"tags": ["Investigators"], "parameters": [{"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "regionFilter", "in": "query", "schema": {"type": "string"}}, {"name": "countryF<PERSON>er", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvestigatorDtoPagedResultApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "post": {"tags": ["Investigators"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateInvestigatorRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateInvestigatorRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateInvestigatorRequest"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvestigatorDtoApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Investigators/sample": {"get": {"tags": ["Investigators"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvestigatorDtoIEnumerableApiResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Investigators/{id}": {"get": {"tags": ["Investigators"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvestigatorDtoApiResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Investigators/search": {"get": {"tags": ["Investigators"], "parameters": [{"name": "searchTerm", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvestigatorDtoIEnumerableApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Investigators/region/{regionName}": {"get": {"tags": ["Investigators"], "parameters": [{"name": "regionName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvestigatorDtoIEnumerableApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Investigators/filters/regions": {"get": {"tags": ["Investigators"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StringIEnumerableApiResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Investigators/filters/countries": {"get": {"tags": ["Investigators"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StringIEnumerableApiResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Investigators/filters": {"get": {"tags": ["Investigators"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvestigatorFiltersDtoApiResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Investigators/health": {"get": {"tags": ["Investigators"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"ApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "traceId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateInvestigatorRequest": {"required": ["investigatorName"], "type": "object", "properties": {"investigatorName": {"maxLength": 200, "minLength": 2, "type": "string"}, "specializationNames": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "designation": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "organisation": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "contactNumber": {"maxLength": 20, "minLength": 0, "type": "string", "format": "tel", "nullable": true}, "emailID": {"maxLength": 255, "minLength": 0, "type": "string", "format": "email", "nullable": true}, "fax": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "regionName": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "countryName": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "stateName": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "cityName": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "InvestigatorDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "investigatorName": {"type": "string", "nullable": true}, "specializationNames": {"type": "string", "nullable": true}, "designation": {"type": "string", "nullable": true}, "organisation": {"type": "string", "nullable": true}, "contactNumber": {"type": "string", "nullable": true}, "emailID": {"type": "string", "nullable": true}, "fax": {"type": "string", "nullable": true}, "regionName": {"type": "string", "nullable": true}, "countryName": {"type": "string", "nullable": true}, "stateName": {"type": "string", "nullable": true}, "cityName": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "fullLocation": {"type": "string", "nullable": true}, "contactInfo": {"type": "string", "nullable": true}}, "additionalProperties": false}, "InvestigatorDtoApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/InvestigatorDto"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "traceId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "InvestigatorDtoIEnumerableApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/InvestigatorDto"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "traceId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "InvestigatorDtoPagedResult": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/InvestigatorDto"}, "nullable": true}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}, "previousPageNumber": {"type": "integer", "format": "int32", "nullable": true, "readOnly": true}, "nextPageNumber": {"type": "integer", "format": "int32", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "InvestigatorDtoPagedResultApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/InvestigatorDtoPagedResult"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "traceId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "InvestigatorFiltersDto": {"type": "object", "properties": {"regions": {"type": "array", "items": {"type": "string"}, "nullable": true}, "countries": {"type": "array", "items": {"type": "string"}, "nullable": true}, "specializations": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "InvestigatorFiltersDtoApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/InvestigatorFiltersDto"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "traceId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringDateTime<>f__AnonymousType2": {"type": "object", "properties": {"status": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "StringIEnumerableApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"type": "string"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "traceId": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Example: \"Authorization: Bearer {token}\"", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}