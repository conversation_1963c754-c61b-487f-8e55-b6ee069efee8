using CIAPI.Modules.UserManagement.Domain.Entities;
using CIAPI.Modules.UserManagement.Domain.Interfaces;
using CIAPI.Modules.UserManagement.Infrastructure.Data;
using Dapper;
using System.Linq.Expressions;

namespace CIAPI.Modules.UserManagement.Infrastructure.Repositories;

/// <summary>
/// Base repository implementation for UserManagement module using ADO.NET with Dapper
/// </summary>
/// <typeparam name="T">Entity type that inherits from BaseEntity</typeparam>
public abstract class BaseUserRepository<T> : IRepository<T> where T : BaseEntity
{
    protected readonly IUserManagementDbConnectionFactory _connectionFactory;
    protected readonly string _tableName;

    protected BaseUserRepository(IUserManagementDbConnectionFactory connectionFactory, string tableName)
    {
        _connectionFactory = connectionFactory ?? throw new ArgumentNullException(nameof(connectionFactory));
        _tableName = tableName ?? throw new ArgumentNullException(nameof(tableName));
    }

    public virtual async Task<T?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = $"SELECT * FROM {_tableName} WHERE Id = @Id AND IsDeleted = 0";
        return await connection.QueryFirstOrDefaultAsync<T>(sql, new { Id = id });
    }

    public virtual async Task<IEnumerable<T>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = $"SELECT * FROM {_tableName} WHERE IsDeleted = 0 ORDER BY CreatedAt DESC";
        return await connection.QueryAsync<T>(sql);
    }

    public virtual async Task<T> AddAsync(T entity, CancellationToken cancellationToken = default)
    {
        entity.CreatedAt = DateTime.UtcNow;
        entity.IsActive = true;
        entity.IsDeleted = false;

        using var connection = _connectionFactory.CreateConnection();
        var insertSql = GenerateInsertSql();
        var id = await connection.QuerySingleAsync<int>(insertSql, entity);
        entity.Id = id;
        return entity;
    }

    public virtual async Task<T> UpdateAsync(T entity, CancellationToken cancellationToken = default)
    {
        entity.UpdatedAt = DateTime.UtcNow;

        using var connection = _connectionFactory.CreateConnection();
        var updateSql = GenerateUpdateSql();
        await connection.ExecuteAsync(updateSql, entity);
        return entity;
    }

    public virtual async Task DeleteAsync(int id, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = $"DELETE FROM {_tableName} WHERE Id = @Id";
        await connection.ExecuteAsync(sql, new { Id = id });
    }

    public virtual async Task DeleteAsync(T entity, CancellationToken cancellationToken = default)
    {
        await DeleteAsync(entity.Id, cancellationToken);
    }

    public virtual async Task SoftDeleteAsync(int id, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = $"UPDATE {_tableName} SET IsDeleted = 1, UpdatedAt = @UpdatedAt WHERE Id = @Id";
        await connection.ExecuteAsync(sql, new { Id = id, UpdatedAt = DateTime.UtcNow });
    }

    public virtual async Task SoftDeleteAsync(T entity, CancellationToken cancellationToken = default)
    {
        await SoftDeleteAsync(entity.Id, cancellationToken);
    }

    public virtual async Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        var count = await CountAsync(predicate, cancellationToken);
        return count > 0;
    }

    public virtual async Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = $"SELECT COUNT(*) FROM {_tableName} WHERE IsDeleted = 0";
        return await connection.QuerySingleAsync<int>(sql);
    }

    // Abstract methods to be implemented by derived classes
    protected abstract string GenerateInsertSql();
    protected abstract string GenerateUpdateSql();

    // Not implemented in base class - would need expression tree parsing
    public virtual Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("FindAsync with expression requires custom implementation in derived class");
    }

    public virtual Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("FirstOrDefaultAsync with expression requires custom implementation in derived class");
    }

    public virtual Task<(IEnumerable<T> Items, int TotalCount)> GetPagedAsync(int pageNumber, int pageSize, Expression<Func<T, bool>>? predicate = null, Expression<Func<T, object>>? orderBy = null, bool ascending = true, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("GetPagedAsync with expressions requires custom implementation in derived class");
    }

    public virtual Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("AddRangeAsync requires custom implementation in derived class");
    }

    public virtual Task<IEnumerable<T>> UpdateRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("UpdateRangeAsync requires custom implementation in derived class");
    }

    public virtual Task DeleteRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("DeleteRangeAsync requires custom implementation in derived class");
    }

    public virtual Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // ADO.NET doesn't have a change tracker, so this is not applicable
        return Task.FromResult(0);
    }
}

/// <summary>
/// User repository implementation using ADO.NET with Dapper for UserManagement module
/// </summary>
public class UserRepository : BaseUserRepository<User>, IUserRepository
{
    public UserRepository(IUserManagementDbConnectionFactory connectionFactory) 
        : base(connectionFactory, "Users")
    {
    }

    public async Task<User?> GetByEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = "SELECT * FROM Users WHERE Email = @Email AND IsDeleted = 0";
        return await connection.QueryFirstOrDefaultAsync<User>(sql, new { Email = email });
    }

    public async Task<User?> GetByPhoneNumberAsync(string phoneNumber, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = "SELECT * FROM Users WHERE PhoneNumber = @PhoneNumber AND IsDeleted = 0";
        return await connection.QueryFirstOrDefaultAsync<User>(sql, new { PhoneNumber = phoneNumber });
    }

    public async Task<bool> EmailExistsAsync(string email, int? excludeUserId = null, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = "SELECT COUNT(*) FROM Users WHERE Email = @Email AND IsDeleted = 0";
        
        if (excludeUserId.HasValue)
        {
            sql += " AND Id != @ExcludeUserId";
        }
        
        var count = await connection.QuerySingleAsync<int>(sql, new { Email = email, ExcludeUserId = excludeUserId });
        return count > 0;
    }

    public async Task<bool> PhoneExistsAsync(string phoneNumber, int? excludeUserId = null, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = "SELECT COUNT(*) FROM Users WHERE PhoneNumber = @PhoneNumber AND IsDeleted = 0";
        
        if (excludeUserId.HasValue)
        {
            sql += " AND Id != @ExcludeUserId";
        }
        
        var count = await connection.QuerySingleAsync<int>(sql, new { PhoneNumber = phoneNumber, ExcludeUserId = excludeUserId });
        return count > 0;
    }

    // Additional UserRepository methods would be implemented here...
    // For brevity, implementing key methods only

    protected override string GenerateInsertSql()
    {
        return @"
            INSERT INTO Users (FirstName, LastName, Email, PhoneNumber, PasswordHash, ProfileImageUrl, 
                             EmailConfirmed, PhoneConfirmed, TwoFactorEnabled, AccessFailedCount, 
                             LockoutEnd, LockoutEnabled, CreatedAt, CreatedBy, IsActive, IsDeleted)
            VALUES (@FirstName, @LastName, @Email, @PhoneNumber, @PasswordHash, @ProfileImageUrl,
                   @EmailConfirmed, @PhoneConfirmed, @TwoFactorEnabled, @AccessFailedCount,
                   @LockoutEnd, @LockoutEnabled, @CreatedAt, @CreatedBy, @IsActive, @IsDeleted);
            SELECT CAST(SCOPE_IDENTITY() as int);";
    }

    protected override string GenerateUpdateSql()
    {
        return @"
            UPDATE Users SET 
                FirstName = @FirstName, LastName = @LastName, Email = @Email, PhoneNumber = @PhoneNumber,
                PasswordHash = @PasswordHash, ProfileImageUrl = @ProfileImageUrl, LastLoginAt = @LastLoginAt,
                EmailConfirmed = @EmailConfirmed, PhoneConfirmed = @PhoneConfirmed, TwoFactorEnabled = @TwoFactorEnabled,
                AccessFailedCount = @AccessFailedCount, LockoutEnd = @LockoutEnd, LockoutEnabled = @LockoutEnabled,
                UpdatedAt = @UpdatedAt, UpdatedBy = @UpdatedBy, IsActive = @IsActive
            WHERE Id = @Id";
    }

    // Placeholder implementations for remaining interface methods
    public Task<IEnumerable<User>> GetUsersByRoleAsync(string roleName, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<IEnumerable<string>> GetUserRolesAsync(int userId, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<IEnumerable<string>> GetUserPermissionsAsync(int userId, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<User?> GetUserWithRolesAsync(int userId, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task UpdateLastLoginAsync(int userId, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task IncrementAccessFailedCountAsync(int userId, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task ResetAccessFailedCountAsync(int userId, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task SetLockoutAsync(int userId, DateTime? lockoutEnd, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }
}
