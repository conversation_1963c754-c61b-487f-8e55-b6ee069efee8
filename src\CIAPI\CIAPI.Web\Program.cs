// ProductManagement Module
using CIAPI.Modules.ProductManagement.Application.Services;
using CIAPI.Modules.ProductManagement.Domain.Interfaces;
using CIAPI.Modules.ProductManagement.Infrastructure.Data;
using CIAPI.Modules.ProductManagement.Infrastructure.Repositories;

// UserManagement Module (placeholder for future implementation)
// using CIAPI.Modules.UserManagement.Application.Services;

using Microsoft.OpenApi.Models;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers();

// Configure OpenAPI/Swagger
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "CI API",
        Version = "v1",
        Description = "Centralized API for CI Solution - A Modern Modular Monolithic Architecture",
        Contact = new OpenApiContact
        {
            Name = "CI Solution Team",
            Email = "<EMAIL>"
        }
    });

    // Add JWT Authentication to Swagger
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            new string[] {}
        }
    });
});

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Register ProductManagement Module Services
builder.Services.AddScoped<IProductManagementDbConnectionFactory, ProductManagementSqlServerConnectionFactory>();
builder.Services.AddScoped<IInvestigatorRepository, InvestigatorRepository>();
builder.Services.AddScoped<IInvestigatorService, InvestigatorService>();

// Register UserManagement Module Services (placeholder for future implementation)
// builder.Services.AddScoped<IUserManagementDbConnectionFactory, UserManagementSqlServerConnectionFactory>();
// builder.Services.AddScoped<IUserRepository, UserRepository>();
// builder.Services.AddScoped<IUserService, UserService>();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "CI API v1");
        c.RoutePrefix = string.Empty; // Set Swagger UI at the app's root
    });
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// Health check endpoint
app.MapGet("/health", () => new { Status = "Healthy", Timestamp = DateTime.UtcNow })
   .WithName("HealthCheck")
   .WithTags("Health");

app.Run();
