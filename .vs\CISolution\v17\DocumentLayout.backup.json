{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{7F812B93-466B-4981-A853-ED139110C588}|src\\CIAPI\\CIAPI.Modules\\ProductManagement\\CIAPI.Modules.ProductManagement.csproj|c:\\users\\<USER>\\documents\\augment-projects\\cisolution\\src\\ciapi\\ciapi.modules\\productmanagement\\infrastructure\\repositories\\investigatorrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7F812B93-466B-4981-A853-ED139110C588}|src\\CIAPI\\CIAPI.Modules\\ProductManagement\\CIAPI.Modules.ProductManagement.csproj|solutionrelative:src\\ciapi\\ciapi.modules\\productmanagement\\infrastructure\\repositories\\investigatorrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{35CFACF5-7F91-49A3-83B9-AAADDA2DC81E}|src\\CIDashboard\\CIDashboard.csproj|c:\\users\\<USER>\\documents\\augment-projects\\cisolution\\src\\cidashboard\\controllers\\userscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{35CFACF5-7F91-49A3-83B9-AAADDA2DC81E}|src\\CIDashboard\\CIDashboard.csproj|solutionrelative:src\\cidashboard\\controllers\\userscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "InvestigatorRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Modules\\ProductManagement\\Infrastructure\\Repositories\\InvestigatorRepository.cs", "RelativeDocumentMoniker": "src\\CIAPI\\CIAPI.Modules\\ProductManagement\\Infrastructure\\Repositories\\InvestigatorRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Modules\\ProductManagement\\Infrastructure\\Repositories\\InvestigatorRepository.cs", "RelativeToolTip": "src\\CIAPI\\CIAPI.Modules\\ProductManagement\\Infrastructure\\Repositories\\InvestigatorRepository.cs", "ViewState": "AgIAAFYAAAAAAAAAAAAUwHEAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T10:39:13.488Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "UsersController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\Controllers\\UsersController.cs", "RelativeDocumentMoniker": "src\\CIDashboard\\Controllers\\UsersController.cs", "ToolTip": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\Controllers\\UsersController.cs", "RelativeToolTip": "src\\CIDashboard\\Controllers\\UsersController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T10:38:57.206Z"}]}]}]}