using CIAPI.Core.Entities;
using CIAPI.Core.Interfaces;
using CIAPI.Infrastructure.Data;
using Dapper;
using System.Data;
using System.Linq.Expressions;

namespace CIAPI.Infrastructure.Repositories;

/// <summary>
/// Investigator repository implementation using ADO.NET with Dapper
/// </summary>
public class InvestigatorRepository : BaseRepository<Investigator>, IInvestigatorRepository
{
    public InvestigatorRepository(IDbConnectionFactory connectionFactory) 
        : base(connectionFactory, "Investigators")
    {
    }

    public async Task<IEnumerable<Investigator>> GetInvestigatorsSampleAsync(CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        
        var sql = "EXEC GetInvestigators_Sample";
        
        return await connection.QueryAsync<Investigator>(sql);
    }

    public async Task<(IEnumerable<Investigator> Items, int TotalCount)> GetInvestigatorsSamplePagedAsync(
        int pageNumber = 1,
        int pageSize = 10,
        string? searchTerm = null,
        string? regionFilter = null,
        string? countryFilter = null,
        CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();

        // First get all data from stored procedure
        var allData = await connection.QueryAsync<Investigator>("EXEC GetInvestigators_Sample");

        // Apply filters in memory (for simplicity)
        var filteredData = allData.AsEnumerable();

        if (!string.IsNullOrEmpty(searchTerm))
        {
            filteredData = filteredData.Where(x => x.InvestigatorName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));
        }

        if (!string.IsNullOrEmpty(regionFilter))
        {
            filteredData = filteredData.Where(x => x.RegionName == regionFilter);
        }

        if (!string.IsNullOrEmpty(countryFilter))
        {
            filteredData = filteredData.Where(x => x.CountryName == countryFilter);
        }

        var totalCount = filteredData.Count();

        // Apply pagination
        var offset = (pageNumber - 1) * pageSize;
        var pagedData = filteredData
            .OrderBy(x => x.InvestigatorName)
            .Skip(offset)
            .Take(pageSize);

        return (pagedData, totalCount);
    }

    public async Task<IEnumerable<Investigator>> GetInvestigatorsByRegionAsync(string regionName, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();

        var allData = await connection.QueryAsync<Investigator>("EXEC GetInvestigators_Sample");

        return allData.Where(x => x.RegionName == regionName).OrderBy(x => x.InvestigatorName);
    }

    public async Task<IEnumerable<Investigator>> GetInvestigatorsByCountryAsync(string countryName, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        
        var sql = @"
            SELECT * 
            FROM (EXEC GetInvestigators_Sample) AS inv
            WHERE CountryName = @CountryName
            ORDER BY InvestigatorName";
        
        return await connection.QueryAsync<Investigator>(sql, new { CountryName = countryName });
    }

    public async Task<IEnumerable<Investigator>> GetInvestigatorsBySpecializationAsync(string specialization, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        
        var sql = @"
            SELECT * 
            FROM (EXEC GetInvestigators_Sample) AS inv
            WHERE SpecializationNames LIKE @Specialization
            ORDER BY InvestigatorName";
        
        return await connection.QueryAsync<Investigator>(sql, new { Specialization = $"%{specialization}%" });
    }

    public async Task<IEnumerable<Investigator>> GetInvestigatorsByOrganisationAsync(string organisation, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        
        var sql = @"
            SELECT * 
            FROM (EXEC GetInvestigators_Sample) AS inv
            WHERE Organisation = @Organisation
            ORDER BY InvestigatorName";
        
        return await connection.QueryAsync<Investigator>(sql, new { Organisation = organisation });
    }

    public async Task<IEnumerable<Investigator>> SearchInvestigatorsByNameAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        
        var sql = @"
            SELECT * 
            FROM (EXEC GetInvestigators_Sample) AS inv
            WHERE InvestigatorName LIKE @SearchTerm
            ORDER BY InvestigatorName";
        
        return await connection.QueryAsync<Investigator>(sql, new { SearchTerm = $"%{searchTerm}%" });
    }

    public async Task<IEnumerable<string>> GetDistinctRegionsAsync(CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();

        var allData = await connection.QueryAsync<Investigator>("EXEC GetInvestigators_Sample");

        return allData
            .Where(x => !string.IsNullOrEmpty(x.RegionName))
            .Select(x => x.RegionName!)
            .Distinct()
            .OrderBy(x => x);
    }

    public async Task<IEnumerable<string>> GetDistinctCountriesAsync(CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        
        var sql = @"
            SELECT DISTINCT CountryName 
            FROM (EXEC GetInvestigators_Sample) AS inv
            WHERE CountryName IS NOT NULL AND CountryName != ''
            ORDER BY CountryName";
        
        return await connection.QueryAsync<string>(sql);
    }

    public async Task<IEnumerable<string>> GetDistinctSpecializationsAsync(CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        
        var sql = @"
            SELECT DISTINCT SpecializationNames 
            FROM (EXEC GetInvestigators_Sample) AS inv
            WHERE SpecializationNames IS NOT NULL AND SpecializationNames != ''
            ORDER BY SpecializationNames";
        
        return await connection.QueryAsync<string>(sql);
    }

    protected override string GenerateInsertSql()
    {
        return @"
            INSERT INTO Investigators (InvestigatorName, SpecializationNames, Designation, Organisation, 
                                     ContactNumber, EmailID, Fax, RegionName, CountryName, StateName, CityName,
                                     CreatedAt, CreatedBy, IsActive, IsDeleted)
            VALUES (@InvestigatorName, @SpecializationNames, @Designation, @Organisation,
                   @ContactNumber, @EmailID, @Fax, @RegionName, @CountryName, @StateName, @CityName,
                   @CreatedAt, @CreatedBy, @IsActive, @IsDeleted);
            SELECT CAST(SCOPE_IDENTITY() as int);";
    }

    protected override string GenerateUpdateSql()
    {
        return @"
            UPDATE Investigators SET 
                InvestigatorName = @InvestigatorName, SpecializationNames = @SpecializationNames,
                Designation = @Designation, Organisation = @Organisation, ContactNumber = @ContactNumber,
                EmailID = @EmailID, Fax = @Fax, RegionName = @RegionName, CountryName = @CountryName,
                StateName = @StateName, CityName = @CityName, UpdatedAt = @UpdatedAt, UpdatedBy = @UpdatedBy,
                IsActive = @IsActive
            WHERE Id = @Id";
    }

    protected override string ConvertExpressionToSql(Expression<Func<Investigator, bool>> predicate)
    {
        // Simplified implementation - in a real application, you'd need a proper expression tree to SQL converter
        return "1=1";
    }
}
