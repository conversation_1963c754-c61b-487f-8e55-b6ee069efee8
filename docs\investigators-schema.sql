-- Investigators Module Database Schema
-- This script creates the Investigators table and GetInvestigators_Sample stored procedure
-- Database: CISolutionDB
-- Version: 1.0.0

USE CISolutionDB;
GO

-- Create Investigators table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Investigators' AND xtype='U')
BEGIN
    CREATE TABLE Investigators (
        Id int IDENTITY(1,1) PRIMARY KEY,
        InvestigatorName nvarchar(200) NOT NULL,
        SpecializationNames nvarchar(500) NULL,
        Designation nvarchar(100) NULL,
        Organisation nvarchar(200) NULL,
        ContactNumber nvarchar(20) NULL,
        EmailID nvarchar(255) NULL,
        Fax nvarchar(20) NULL,
        RegionName nvarchar(100) NULL,
        CountryName nvarchar(100) NULL,
        StateName nvarchar(100) NULL,
        CityName nvarchar(100) NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL,
        CreatedBy nvarchar(100) NULL,
        UpdatedBy nvarchar(100) NULL,
        IsActive bit NOT NULL DEFAULT 1,
        IsDeleted bit NOT NULL DEFAULT 0,
        RowVersion rowversion
    );
    
    -- Create indexes for better performance
    CREATE INDEX IX_Investigators_InvestigatorName ON Investigators(InvestigatorName);
    CREATE INDEX IX_Investigators_RegionName ON Investigators(RegionName);
    CREATE INDEX IX_Investigators_CountryName ON Investigators(CountryName);
    CREATE INDEX IX_Investigators_Organisation ON Investigators(Organisation);
    CREATE INDEX IX_Investigators_EmailID ON Investigators(EmailID);
    CREATE INDEX IX_Investigators_IsActive ON Investigators(IsActive);
    CREATE INDEX IX_Investigators_IsDeleted ON Investigators(IsDeleted);
    CREATE INDEX IX_Investigators_CreatedAt ON Investigators(CreatedAt);
    
    PRINT 'Investigators table created successfully!';
END
ELSE
BEGIN
    PRINT 'Investigators table already exists.';
END
GO

-- Create or alter the GetInvestigators_Sample stored procedure
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'GetInvestigators_Sample')
BEGIN
    DROP PROCEDURE GetInvestigators_Sample;
END
GO

CREATE PROCEDURE GetInvestigators_Sample
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Return all investigators with the specified columns
        SELECT 
            InvestigatorName,
            SpecializationNames,
            Designation,
            Organisation,
            ContactNumber,
            EmailID,
            Fax,
            RegionName,
            CountryName,
            StateName,
            CityName
        FROM Investigators
        WHERE IsDeleted = 0
        ORDER BY InvestigatorName;
        
    END TRY
    BEGIN CATCH
        -- Error handling
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
GO

-- Insert sample data for testing
IF NOT EXISTS (SELECT * FROM Investigators WHERE InvestigatorName = 'Dr. John Smith')
BEGIN
    INSERT INTO Investigators (
        InvestigatorName, SpecializationNames, Designation, Organisation, 
        ContactNumber, EmailID, Fax, RegionName, CountryName, StateName, CityName,
        CreatedBy, IsActive
    ) VALUES 
    ('Dr. John Smith', 'Cardiology, Internal Medicine', 'Senior Consultant', 'City General Hospital', 
     '+1-555-0101', '<EMAIL>', '+1-555-0102', 'North America', 'United States', 'California', 'Los Angeles',
     'System', 1),
    
    ('Dr. Sarah Johnson', 'Neurology, Neurosurgery', 'Department Head', 'Metro Medical Center', 
     '+1-555-0201', '<EMAIL>', '+1-555-0202', 'North America', 'United States', 'New York', 'New York City',
     'System', 1),
    
    ('Dr. Michael Chen', 'Oncology, Hematology', 'Research Director', 'Cancer Research Institute', 
     '+1-555-0301', '<EMAIL>', '+1-555-0302', 'North America', 'Canada', 'Ontario', 'Toronto',
     'System', 1),
    
    ('Dr. Emily Davis', 'Pediatrics, Neonatology', 'Chief of Pediatrics', 'Children''s Hospital', 
     '+44-20-7946-0958', '<EMAIL>', '+44-20-7946-0959', 'Europe', 'United Kingdom', 'England', 'London',
     'System', 1),
    
    ('Dr. Robert Wilson', 'Orthopedics, Sports Medicine', 'Senior Surgeon', 'Sports Medicine Clinic', 
     '+61-2-9374-4000', '<EMAIL>', '+61-2-9374-4001', 'Asia Pacific', 'Australia', 'New South Wales', 'Sydney',
     'System', 1),
    
    ('Dr. Maria Garcia', 'Dermatology, Cosmetic Surgery', 'Practice Owner', 'Advanced Dermatology', 
     '+34-91-123-4567', '<EMAIL>', '+34-91-123-4568', 'Europe', 'Spain', 'Madrid', 'Madrid',
     'System', 1),
    
    ('Dr. David Kumar', 'Gastroenterology, Hepatology', 'Associate Professor', 'University Medical College', 
     '+91-11-2345-6789', '<EMAIL>', '+91-11-2345-6790', 'Asia Pacific', 'India', 'Delhi', 'New Delhi',
     'System', 1),
    
    ('Dr. Lisa Anderson', 'Psychiatry, Psychology', 'Mental Health Director', 'Wellness Center', 
     '+1-555-0401', '<EMAIL>', '+1-555-0402', 'North America', 'United States', 'Texas', 'Houston',
     'System', 1),
    
    ('Dr. James Thompson', 'Emergency Medicine, Trauma Surgery', 'Emergency Department Chief', 'Regional Hospital', 
     '+1-555-0501', '<EMAIL>', '+1-555-0502', 'North America', 'United States', 'Florida', 'Miami',
     'System', 1),
    
    ('Dr. Anna Müller', 'Radiology, Nuclear Medicine', 'Imaging Director', 'Diagnostic Center', 
     '+49-30-12345678', '<EMAIL>', '+49-30-12345679', 'Europe', 'Germany', 'Berlin', 'Berlin',
     'System', 1);
    
    PRINT 'Sample investigator data inserted successfully!';
END
ELSE
BEGIN
    PRINT 'Sample investigator data already exists.';
END
GO

-- Test the stored procedure
PRINT 'Testing GetInvestigators_Sample stored procedure...';
EXEC GetInvestigators_Sample;
GO

PRINT 'Investigators module database setup completed successfully!';
PRINT 'Total investigators in database: ' + CAST((SELECT COUNT(*) FROM Investigators WHERE IsDeleted = 0) AS NVARCHAR(10));
GO
