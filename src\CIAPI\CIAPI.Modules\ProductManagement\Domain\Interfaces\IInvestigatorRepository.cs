using CIAPI.Modules.ProductManagement.Domain.Entities;
using System.Linq.Expressions;

namespace CIAPI.Modules.ProductManagement.Domain.Interfaces;

/// <summary>
/// Generic repository interface for common CRUD operations
/// </summary>
/// <typeparam name="T">Entity type that inherits from BaseEntity</typeparam>
public interface IRepository<T> where T : BaseEntity
{
    // Query operations
    Task<T?> GetByIdAsync(int id, CancellationToken cancellationToken = default);
    Task<IEnumerable<T>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default);
    Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default);
    Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null, CancellationToken cancellationToken = default);
    
    // Paging operations
    Task<(IEnumerable<T> Items, int TotalCount)> GetPagedAsync(
        int pageNumber, 
        int pageSize, 
        Expression<Func<T, bool>>? predicate = null,
        Expression<Func<T, object>>? orderBy = null,
        bool ascending = true,
        CancellationToken cancellationToken = default);
    
    // Command operations
    Task<T> AddAsync(T entity, CancellationToken cancellationToken = default);
    Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default);
    Task<T> UpdateAsync(T entity, CancellationToken cancellationToken = default);
    Task<IEnumerable<T>> UpdateRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default);
    Task DeleteAsync(int id, CancellationToken cancellationToken = default);
    Task DeleteAsync(T entity, CancellationToken cancellationToken = default);
    Task DeleteRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default);
    Task SoftDeleteAsync(int id, CancellationToken cancellationToken = default);
    Task SoftDeleteAsync(T entity, CancellationToken cancellationToken = default);
    
    // Transaction support
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Investigator-specific repository interface with additional investigator operations
/// </summary>
public interface IInvestigatorRepository : IRepository<Investigator>
{
    /// <summary>
    /// Get investigators using the GetInvestigators_Sample stored procedure
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of investigators from stored procedure</returns>
    Task<IEnumerable<Investigator>> GetInvestigatorsSampleAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get investigators by region
    /// </summary>
    /// <param name="regionName">Region name to filter by</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of investigators in the specified region</returns>
    Task<IEnumerable<Investigator>> GetInvestigatorsByRegionAsync(string regionName, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get investigators by country
    /// </summary>
    /// <param name="countryName">Country name to filter by</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of investigators in the specified country</returns>
    Task<IEnumerable<Investigator>> GetInvestigatorsByCountryAsync(string countryName, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get investigators by specialization
    /// </summary>
    /// <param name="specialization">Specialization to filter by</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of investigators with the specified specialization</returns>
    Task<IEnumerable<Investigator>> GetInvestigatorsBySpecializationAsync(string specialization, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get investigators by organization
    /// </summary>
    /// <param name="organisation">Organization name to filter by</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of investigators from the specified organization</returns>
    Task<IEnumerable<Investigator>> GetInvestigatorsByOrganisationAsync(string organisation, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Search investigators by name
    /// </summary>
    /// <param name="searchTerm">Search term for investigator name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of investigators matching the search term</returns>
    Task<IEnumerable<Investigator>> SearchInvestigatorsByNameAsync(string searchTerm, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get paginated investigators using stored procedure
    /// </summary>
    /// <param name="pageNumber">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="searchTerm">Optional search term</param>
    /// <param name="regionFilter">Optional region filter</param>
    /// <param name="countryFilter">Optional country filter</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated result of investigators</returns>
    Task<(IEnumerable<Investigator> Items, int TotalCount)> GetInvestigatorsSamplePagedAsync(
        int pageNumber = 1, 
        int pageSize = 10, 
        string? searchTerm = null,
        string? regionFilter = null,
        string? countryFilter = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get distinct regions from investigators
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of distinct region names</returns>
    Task<IEnumerable<string>> GetDistinctRegionsAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get distinct countries from investigators
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of distinct country names</returns>
    Task<IEnumerable<string>> GetDistinctCountriesAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get distinct specializations from investigators
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of distinct specializations</returns>
    Task<IEnumerable<string>> GetDistinctSpecializationsAsync(CancellationToken cancellationToken = default);
}
