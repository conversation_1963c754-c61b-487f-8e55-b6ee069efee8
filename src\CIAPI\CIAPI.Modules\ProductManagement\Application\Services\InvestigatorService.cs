using CIAPI.Modules.ProductManagement.Domain.Entities;
using CIAPI.Modules.ProductManagement.Domain.Interfaces;
using CIAPI.Modules.ProductManagement.Application.DTOs;
using CIAPI.Shared.DTOs.Common;
using Microsoft.Extensions.Logging;

namespace CIAPI.Modules.ProductManagement.Application.Services;

/// <summary>
/// Investigator service implementation
/// </summary>
public class InvestigatorService : IInvestigatorService
{
    private readonly IInvestigatorRepository _investigatorRepository;
    private readonly ILogger<InvestigatorService> _logger;

    public InvestigatorService(IInvestigatorRepository investigatorRepository, ILogger<InvestigatorService> logger)
    {
        _investigatorRepository = investigatorRepository ?? throw new ArgumentNullException(nameof(investigatorRepository));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<ApiResponse<InvestigatorDto>> GetInvestigatorByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting investigator with ID: {InvestigatorId}", id);

            if (id <= 0)
                return ApiResponse<InvestigatorDto>.ErrorResult("Invalid investigator ID");

            var investigator = await _investigatorRepository.GetByIdAsync(id, cancellationToken);

            if (investigator == null)
                return ApiResponse<InvestigatorDto>.ErrorResult("Investigator not found");

            var dto = MapToDto(investigator);
            return ApiResponse<InvestigatorDto>.SuccessResult(dto, "Investigator retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting investigator with ID: {InvestigatorId}", id);
            return ApiResponse<InvestigatorDto>.ErrorResult("An error occurred while retrieving the investigator");
        }
    }

    public async Task<ApiResponse<PagedResult<InvestigatorDto>>> GetInvestigatorsAsync(PaginationParameters parameters, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting investigators with pagination. Page: {PageNumber}, Size: {PageSize}", 
                parameters.PageNumber, parameters.PageSize);

            // For now, use the stored procedure method as the main source
            var request = new InvestigatorSearchRequest
            {
                PageNumber = parameters.PageNumber,
                PageSize = parameters.PageSize,
                SearchTerm = parameters.SearchTerm,
                SortDescending = parameters.SortDescending
            };

            return await GetInvestigatorsSamplePagedAsync(request, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting investigators");
            return ApiResponse<PagedResult<InvestigatorDto>>.ErrorResult("An error occurred while retrieving investigators");
        }
    }

    public async Task<ApiResponse<IEnumerable<InvestigatorDto>>> GetInvestigatorsSampleAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting investigators using GetInvestigators_Sample stored procedure");

            var investigators = await _investigatorRepository.GetInvestigatorsSampleAsync(cancellationToken);
            var dtos = investigators.Select(MapToDto);

            return ApiResponse<IEnumerable<InvestigatorDto>>.SuccessResult(dtos, "Investigators retrieved successfully from stored procedure");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting investigators from stored procedure");
            return ApiResponse<IEnumerable<InvestigatorDto>>.ErrorResult("An error occurred while retrieving investigators from stored procedure");
        }
    }

    public async Task<ApiResponse<PagedResult<InvestigatorDto>>> GetInvestigatorsSamplePagedAsync(InvestigatorSearchRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting paginated investigators using stored procedure. Page: {PageNumber}, Size: {PageSize}", 
                request.PageNumber, request.PageSize);

            var (items, totalCount) = await _investigatorRepository.GetInvestigatorsSamplePagedAsync(
                request.PageNumber,
                request.PageSize,
                request.SearchTerm,
                request.RegionFilter,
                request.CountryFilter,
                cancellationToken);

            var dtos = items.Select(MapToDto);
            var pagedResult = new PagedResult<InvestigatorDto>(dtos, request.PageNumber, request.PageSize, totalCount);

            return ApiResponse<PagedResult<InvestigatorDto>>.SuccessResult(pagedResult, "Investigators retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting paginated investigators from stored procedure");
            return ApiResponse<PagedResult<InvestigatorDto>>.ErrorResult("An error occurred while retrieving investigators");
        }
    }

    public async Task<ApiResponse<InvestigatorDto>> CreateInvestigatorAsync(CreateInvestigatorRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating new investigator: {InvestigatorName}", request.InvestigatorName);

            var investigator = new Investigator
            {
                InvestigatorName = request.InvestigatorName,
                SpecializationNames = request.SpecializationNames,
                Designation = request.Designation,
                Organisation = request.Organisation,
                ContactNumber = request.ContactNumber,
                EmailID = request.EmailID,
                Fax = request.Fax,
                RegionName = request.RegionName,
                CountryName = request.CountryName,
                StateName = request.StateName,
                CityName = request.CityName,
                IsActive = request.IsActive,
                CreatedAt = DateTime.UtcNow
            };

            var createdInvestigator = await _investigatorRepository.AddAsync(investigator, cancellationToken);
            var dto = MapToDto(createdInvestigator);

            return ApiResponse<InvestigatorDto>.SuccessResult(dto, "Investigator created successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while creating investigator");
            return ApiResponse<InvestigatorDto>.ErrorResult("An error occurred while creating the investigator");
        }
    }

    public async Task<ApiResponse<IEnumerable<InvestigatorDto>>> SearchInvestigatorsByNameAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Searching investigators by name: {SearchTerm}", searchTerm);

            var investigators = await _investigatorRepository.SearchInvestigatorsByNameAsync(searchTerm, cancellationToken);
            var dtos = investigators.Select(MapToDto);
            
            return ApiResponse<IEnumerable<InvestigatorDto>>.SuccessResult(dtos, "Search completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while searching investigators by name");
            return ApiResponse<IEnumerable<InvestigatorDto>>.ErrorResult("An error occurred while searching investigators");
        }
    }

    public async Task<ApiResponse<IEnumerable<InvestigatorDto>>> GetInvestigatorsByRegionAsync(string regionName, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting investigators by region: {RegionName}", regionName);

            var investigators = await _investigatorRepository.GetInvestigatorsByRegionAsync(regionName, cancellationToken);
            var dtos = investigators.Select(MapToDto);
            
            return ApiResponse<IEnumerable<InvestigatorDto>>.SuccessResult(dtos, "Investigators retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting investigators by region");
            return ApiResponse<IEnumerable<InvestigatorDto>>.ErrorResult("An error occurred while retrieving investigators");
        }
    }

    public async Task<ApiResponse<IEnumerable<string>>> GetDistinctRegionsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting distinct regions");

            var regions = await _investigatorRepository.GetDistinctRegionsAsync(cancellationToken);
            return ApiResponse<IEnumerable<string>>.SuccessResult(regions, "Regions retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting distinct regions");
            return ApiResponse<IEnumerable<string>>.ErrorResult("An error occurred while retrieving regions");
        }
    }

    public async Task<ApiResponse<IEnumerable<string>>> GetDistinctCountriesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting distinct countries");

            var countries = await _investigatorRepository.GetDistinctCountriesAsync(cancellationToken);
            return ApiResponse<IEnumerable<string>>.SuccessResult(countries, "Countries retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting distinct countries");
            return ApiResponse<IEnumerable<string>>.ErrorResult("An error occurred while retrieving countries");
        }
    }

    public async Task<ApiResponse<InvestigatorFiltersDto>> GetInvestigatorFiltersAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting investigator filters");

            var regions = await _investigatorRepository.GetDistinctRegionsAsync(cancellationToken);
            var countries = await _investigatorRepository.GetDistinctCountriesAsync(cancellationToken);
            var specializations = await _investigatorRepository.GetDistinctSpecializationsAsync(cancellationToken);

            var filters = new InvestigatorFiltersDto
            {
                Regions = regions,
                Countries = countries,
                Specializations = specializations
            };

            return ApiResponse<InvestigatorFiltersDto>.SuccessResult(filters, "Filters retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting investigator filters");
            return ApiResponse<InvestigatorFiltersDto>.ErrorResult("An error occurred while retrieving filters");
        }
    }

    private static InvestigatorDto MapToDto(Investigator investigator)
    {
        return new InvestigatorDto
        {
            Id = investigator.Id,
            InvestigatorName = investigator.InvestigatorName,
            SpecializationNames = investigator.SpecializationNames,
            Designation = investigator.Designation,
            Organisation = investigator.Organisation,
            ContactNumber = investigator.ContactNumber,
            EmailID = investigator.EmailID,
            Fax = investigator.Fax,
            RegionName = investigator.RegionName,
            CountryName = investigator.CountryName,
            StateName = investigator.StateName,
            CityName = investigator.CityName,
            IsActive = investigator.IsActive,
            CreatedAt = investigator.CreatedAt,
            UpdatedAt = investigator.UpdatedAt,
            FullLocation = investigator.FullLocation,
            ContactInfo = investigator.ContactInfo
        };
    }

    // Placeholder implementations for remaining interface methods
    public Task<ApiResponse<InvestigatorDto>> UpdateInvestigatorAsync(int id, UpdateInvestigatorRequest request, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("UpdateInvestigatorAsync not yet implemented");
    }

    public Task<ApiResponse> DeleteInvestigatorAsync(int id, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("DeleteInvestigatorAsync not yet implemented");
    }

    public Task<ApiResponse> ActivateInvestigatorAsync(int id, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("ActivateInvestigatorAsync not yet implemented");
    }

    public Task<ApiResponse> DeactivateInvestigatorAsync(int id, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("DeactivateInvestigatorAsync not yet implemented");
    }

    public Task<ApiResponse<IEnumerable<InvestigatorDto>>> GetInvestigatorsByCountryAsync(string countryName, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("GetInvestigatorsByCountryAsync not yet implemented");
    }

    public Task<ApiResponse<IEnumerable<InvestigatorDto>>> GetInvestigatorsBySpecializationAsync(string specialization, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("GetInvestigatorsBySpecializationAsync not yet implemented");
    }

    public Task<ApiResponse<IEnumerable<InvestigatorDto>>> GetInvestigatorsByOrganisationAsync(string organisation, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("GetInvestigatorsByOrganisationAsync not yet implemented");
    }

    public Task<ApiResponse<IEnumerable<string>>> GetDistinctSpecializationsAsync(CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("GetDistinctSpecializationsAsync not yet implemented");
    }

    public Task<ApiResponse<InvestigatorStatsDto>> GetInvestigatorStatsAsync(CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("GetInvestigatorStatsAsync not yet implemented");
    }

    public Task<ApiResponse<bool>> ValidateInvestigatorEmailAsync(string email, int? excludeInvestigatorId = null, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("ValidateInvestigatorEmailAsync not yet implemented");
    }

    public Task<ApiResponse<bool>> ValidateInvestigatorContactAsync(string contactNumber, int? excludeInvestigatorId = null, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("ValidateInvestigatorContactAsync not yet implemented");
    }
}
