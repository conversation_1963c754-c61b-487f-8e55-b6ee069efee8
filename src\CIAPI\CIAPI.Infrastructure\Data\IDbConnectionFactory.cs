using System.Data;

namespace CIAPI.Infrastructure.Data;

/// <summary>
/// Factory interface for creating database connections
/// </summary>
public interface IDbConnectionFactory
{
    /// <summary>
    /// Creates a new database connection
    /// </summary>
    /// <returns>Database connection instance</returns>
    IDbConnection CreateConnection();
    
    /// <summary>
    /// Creates a new database connection asynchronously
    /// </summary>
    /// <returns>Database connection instance</returns>
    Task<IDbConnection> CreateConnectionAsync();
    
    /// <summary>
    /// Gets the connection string
    /// </summary>
    string ConnectionString { get; }
}
