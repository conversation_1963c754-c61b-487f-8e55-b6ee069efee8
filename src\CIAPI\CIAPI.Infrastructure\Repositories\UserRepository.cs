using CIAPI.Core.Entities;
using CIAPI.Core.Interfaces;
using CIAPI.Infrastructure.Data;
using Dapper;
using System.Linq.Expressions;

namespace CIAPI.Infrastructure.Repositories;

/// <summary>
/// User repository implementation using ADO.NET with Dapper
/// </summary>
public class UserRepository : BaseRepository<User>, IUserRepository
{
    public UserRepository(IDbConnectionFactory connectionFactory) 
        : base(connectionFactory, "Users")
    {
    }

    public async Task<User?> GetByEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = "SELECT * FROM Users WHERE Email = @Email AND IsDeleted = 0";
        return await connection.QueryFirstOrDefaultAsync<User>(sql, new { Email = email });
    }

    public async Task<User?> GetByPhoneNumberAsync(string phoneNumber, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = "SELECT * FROM Users WHERE PhoneNumber = @PhoneNumber AND IsDeleted = 0";
        return await connection.QueryFirstOrDefaultAsync<User>(sql, new { PhoneNumber = phoneNumber });
    }

    public async Task<bool> EmailExistsAsync(string email, int? excludeUserId = null, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = "SELECT COUNT(*) FROM Users WHERE Email = @Email AND IsDeleted = 0";
        
        if (excludeUserId.HasValue)
        {
            sql += " AND Id != @ExcludeUserId";
        }
        
        var count = await connection.QuerySingleAsync<int>(sql, new { Email = email, ExcludeUserId = excludeUserId });
        return count > 0;
    }

    public async Task<bool> PhoneExistsAsync(string phoneNumber, int? excludeUserId = null, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = "SELECT COUNT(*) FROM Users WHERE PhoneNumber = @PhoneNumber AND IsDeleted = 0";
        
        if (excludeUserId.HasValue)
        {
            sql += " AND Id != @ExcludeUserId";
        }
        
        var count = await connection.QuerySingleAsync<int>(sql, new { PhoneNumber = phoneNumber, ExcludeUserId = excludeUserId });
        return count > 0;
    }

    public async Task<IEnumerable<User>> GetUsersByRoleAsync(string roleName, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = @"
            SELECT u.* FROM Users u
            INNER JOIN UserRoles ur ON u.Id = ur.UserId
            INNER JOIN Roles r ON ur.RoleId = r.Id
            WHERE r.Name = @RoleName AND u.IsDeleted = 0 AND r.IsDeleted = 0";
        
        return await connection.QueryAsync<User>(sql, new { RoleName = roleName });
    }

    public async Task<IEnumerable<string>> GetUserRolesAsync(int userId, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = @"
            SELECT r.Name FROM Roles r
            INNER JOIN UserRoles ur ON r.Id = ur.RoleId
            WHERE ur.UserId = @UserId AND r.IsDeleted = 0";
        
        return await connection.QueryAsync<string>(sql, new { UserId = userId });
    }

    public async Task<IEnumerable<string>> GetUserPermissionsAsync(int userId, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = @"
            SELECT DISTINCT p.Name FROM Permissions p
            INNER JOIN RolePermissions rp ON p.Id = rp.PermissionId
            INNER JOIN UserRoles ur ON rp.RoleId = ur.RoleId
            WHERE ur.UserId = @UserId AND p.IsDeleted = 0";
        
        return await connection.QueryAsync<string>(sql, new { UserId = userId });
    }

    public async Task<User?> GetUserWithRolesAsync(int userId, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = @"
            SELECT u.*, r.Id, r.Name, r.Description 
            FROM Users u
            LEFT JOIN UserRoles ur ON u.Id = ur.UserId
            LEFT JOIN Roles r ON ur.RoleId = r.Id
            WHERE u.Id = @UserId AND u.IsDeleted = 0";
        
        var userDictionary = new Dictionary<int, User>();
        
        var users = await connection.QueryAsync<User, Role, User>(sql,
            (user, role) =>
            {
                if (!userDictionary.TryGetValue(user.Id, out var userEntry))
                {
                    userEntry = user;
                    userEntry.UserRoles = new List<UserRole>();
                    userDictionary.Add(user.Id, userEntry);
                }
                
                if (role != null)
                {
                    userEntry.UserRoles.Add(new UserRole { UserId = user.Id, RoleId = role.Id, Role = role });
                }
                
                return userEntry;
            },
            new { UserId = userId },
            splitOn: "Id");
        
        return userDictionary.Values.FirstOrDefault();
    }

    public async Task UpdateLastLoginAsync(int userId, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = "UPDATE Users SET LastLoginAt = @LastLoginAt, UpdatedAt = @UpdatedAt WHERE Id = @UserId";
        await connection.ExecuteAsync(sql, new { UserId = userId, LastLoginAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow });
    }

    public async Task IncrementAccessFailedCountAsync(int userId, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = "UPDATE Users SET AccessFailedCount = AccessFailedCount + 1, UpdatedAt = @UpdatedAt WHERE Id = @UserId";
        await connection.ExecuteAsync(sql, new { UserId = userId, UpdatedAt = DateTime.UtcNow });
    }

    public async Task ResetAccessFailedCountAsync(int userId, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = "UPDATE Users SET AccessFailedCount = 0, UpdatedAt = @UpdatedAt WHERE Id = @UserId";
        await connection.ExecuteAsync(sql, new { UserId = userId, UpdatedAt = DateTime.UtcNow });
    }

    public async Task SetLockoutAsync(int userId, DateTime? lockoutEnd, CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        var sql = "UPDATE Users SET LockoutEnd = @LockoutEnd, UpdatedAt = @UpdatedAt WHERE Id = @UserId";
        await connection.ExecuteAsync(sql, new { UserId = userId, LockoutEnd = lockoutEnd, UpdatedAt = DateTime.UtcNow });
    }

    protected override string GenerateInsertSql()
    {
        return @"
            INSERT INTO Users (FirstName, LastName, Email, PhoneNumber, PasswordHash, ProfileImageUrl, 
                             EmailConfirmed, PhoneConfirmed, TwoFactorEnabled, AccessFailedCount, 
                             LockoutEnd, LockoutEnabled, CreatedAt, CreatedBy, IsActive, IsDeleted)
            VALUES (@FirstName, @LastName, @Email, @PhoneNumber, @PasswordHash, @ProfileImageUrl,
                   @EmailConfirmed, @PhoneConfirmed, @TwoFactorEnabled, @AccessFailedCount,
                   @LockoutEnd, @LockoutEnabled, @CreatedAt, @CreatedBy, @IsActive, @IsDeleted);
            SELECT CAST(SCOPE_IDENTITY() as int);";
    }

    protected override string GenerateUpdateSql()
    {
        return @"
            UPDATE Users SET 
                FirstName = @FirstName, LastName = @LastName, Email = @Email, PhoneNumber = @PhoneNumber,
                PasswordHash = @PasswordHash, ProfileImageUrl = @ProfileImageUrl, LastLoginAt = @LastLoginAt,
                EmailConfirmed = @EmailConfirmed, PhoneConfirmed = @PhoneConfirmed, TwoFactorEnabled = @TwoFactorEnabled,
                AccessFailedCount = @AccessFailedCount, LockoutEnd = @LockoutEnd, LockoutEnabled = @LockoutEnabled,
                UpdatedAt = @UpdatedAt, UpdatedBy = @UpdatedBy, IsActive = @IsActive
            WHERE Id = @Id";
    }

    protected override string ConvertExpressionToSql(Expression<Func<User, bool>> predicate)
    {
        // Simplified implementation - in a real application, you'd need a proper expression tree to SQL converter
        // For now, return a basic condition
        return "1=1";
    }
}
